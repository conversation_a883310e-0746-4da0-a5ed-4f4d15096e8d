---
description: 
globs: 
alwaysApply: false
---
# 整体说明
你是一个集前端开发专家、交互设计师、产品经理为一体的全能手，现在需要帮我做一个网站，这个网页是一个小科狗输入法软件的官方网站，这个是免费输入法，我们有准备过一些相关的资料信息了，需要从这些资料信息里面提出网页网页的内并开发出整个软件产品的官方网站。

# 已有信息内容：
- 小科狗输入法平台使用说明：https://www.yuque.com/gongzixiaobai-ypdqo/tnrbgm/yfqze3
- 小科输入法平台使用说明：https://zhuanlan.zhihu.com/p/25084621174
- 小科狗今天头条说明：https://www.toutiao.com/c/user/token/MS4wLjABAAAAsItj-RKKszxsyp_4Y_D82QpBTYN4X13J_rt3giogb1B1ZEiJDQZTJAQLw_ZJv1SP
- 小科狗输入法平台B站视频使用说明：https://www.bilibili.com/video/BV1rrPAejEEv/
- 小科狗输入法平台（非官网）方案：简陋版 https://zhang-yuxin.com/keg.html，现代版 https://zhang-yuxin.com/keg2.html

# 任务
1、按要求规划整个网站内容版块和页面结构，从已有的信息内容里整理出网站内容，填到网站版块里面
2、制作出整体网站页面

# 网站要求：
1、网页的内容是关于小科狗输入法软件，相关的介绍内容可以从已有信息内容里面提取和整理，你作为产品经理有自己的思路；
2、内容不需把已有信息全部拿来堆积，你作为一个产品经理角色，根据网站内容版块和页面结构设计，合理提取整理对应的内容；
3、网站名字叫”小科狗输入法“，网址是xiaohegou.com，底部需有页脚用于放版权\组织信息\连接等；
4、这个免费的，有对应的圈子，提供生态圈的相应信息和入口，比如QQ群\B站视频等；
5、考虑到网站流量，本地不存放视频文件，网站上也不播放，但考虑教程的需要，可以连接到对应的视频教程
6、网站需要体现大气、专业、面向互联网技术；

# 技术要求：
1、开发的是静态网站，技术仅用原生的html/css/js，不引入复杂的框架，减少体积加快访问速度； 
2、支持自适应，特别是手机端查看；
3、已有信息内容里面有很多内容用到图片，如果网站也要用到对应的图片，请下载到工程对应的图片文件夹，再网页里面引用；

4、你可以有自己的任务拆解，按照自己的计划来，但是所有功能一次性输出结果，中间不要停下来询问我