# 小科狗输入法平台使用说明

<font style="color:#E8323C;">【QQ群】欢迎加入小科狗官方QQ群交流，网友互动活跃，小科狗开发者、本文档维护者，都在群内积极互动。群号：</font>**<font style="color:#E8323C;">641858743 </font>**<font style="color:#E8323C;">。或扫描以下二维码加入。</font>

![1747791753681-b6ae40fe-b115-4221-9b98-b2ad5ef48edc.png](./img/DaV-vhgy2BzJ45hC/1747791753681-b6ae40fe-b115-4221-9b98-b2ad5ef48edc-448405.png)



[知乎平台小科狗输入法文字说明](https://zhuanlan.zhihu.com/p/25084621174)

<font style="color:#E8323C;">【视频教程】另，小科狗输入法平台使用说明视频版正在更新当中，欢迎收看</font>：

小科狗输入法平台在线视频教程：

 

[（桃子版）小科狗今日头条视频说明](https://www.toutiao.com/c/user/token/MS4wLjABAAAAsItj-RKKszxsyp_4Y_D82QpBTYN4X13J_rt3giogb1B1ZEiJDQZTJAQLw_ZJv1SP/?log_from=01f2881be33f1_1709693888321&tab=video)

[（毛博士版）小科狗B站视频说明](https://www.bilibili.com/video/BV1rrPAejEEv/?share_source=copy_web&vd_source=45d762a24a3f9905c6741bca18a74e9b)

# 小科狗简介
小科狗输入法平台，又名为 Keg 输入法（取 Keep Earth Green 首字母组合），是一款基于 C++，专为Windows 系统打造，融合了小狼毫、影子、大科狗等输入法平台特点的纯TSF架构的输入法平台。

小科狗与其他输入法有本质的不同，其目标是打造一款挂码方便、码字爽快和打遍天下所有编码方案的输入法平台。

该款输入法平台具有以下其它输入法或输入法平台具有或者不具有的**特色功能**：

1. win7-win11通用：纯TSF架构，专为Windows系统打造，兼容性强。
2. 符号码元全覆盖；可以挂接大小键盘符号键位码元编码方案（除整句和并击方案外），支持全汉字编码方案，支持仓颉编码方案的键首字根编码串显示。
3. 支持文本码表直接从候选窗口导入：同时支持用sqlite数据库工具导入多格式的大数据词条，如脚本代码片断或带格式的诗歌词条等。
4. 支持20万汉字字符全字集显示：字体兼容GDI、GDI+和Direct2D三种模式绘制候选。在相应字体库的支持下，可以在不同系统设置不同的绘制模式，显示所有汉字字符，包括彩色的表情字符。
5. 为每一进程分配各自独立的编码方案：不同软件之间码表相互独立，同一码表不同软件之间中英文状态相互独立：例如在Word文档里面利用英文码表纯英文码字的同时，切换到QQ或者微信时可以用86五笔中文聊天；或者Word文档里面和QQ同时用86五笔输入法，但是Word里面为英文状态，而QQ里面为中文状态，互不影响。
6. 首个全中文文字配置和脚本控件界面配置：支持文字配置增量化配置，不需要逐一去给每次更新、每个码表进行配置，只需将其他人配置好的内容复制粘贴到配置框中，点击应用，即可轻松享受他人配置精美的界面、相关功能等。脚本控件界面配置助手可任意改写，扩展相应功能。
7. 候选自动颠倒功能：候选框根据光标所处位置进行自动调整，使得第一候选项永远离输入框最近。首创界面虚拟区域显示，可有效避免候选窗口闪炼，带来很爽的视觉效果。
8. 超级命令直通车：支持采用命令行命令制作各种命令直通车，也可以采用各种脚本解译器执行带参数传送的脚本代码片断，制作复杂的超级命令直通车。
9. 首创双检索功能：具有独特的联想功能，并支持上屏历史字符串与编码串为条件进行联合双检索，可有效地降低音码编码方案的选重率。
10. 主副码表切换与码表引导功能：支持主副两个编码方案轮流切换，支持12个码表引导挂打，引导方案码字非常方便，每一个方案的挂打均由各自配置独立控制。
11. 截图上屏功能：支持候选窗口直接截图上屏，支持字词码字截图上屏。
12. …因为小科狗可玩性太高，许多特色功能有待开发者和使用者进一步总结和发现。

---

# 小科狗快捷键一览表
小科狗默认快捷键：

请打开小科狗安装包里面的“<font style="color:rgb(38, 38, 38);">keg.txt</font>”，如下图，为小科狗快捷键默认配置。

![1686038259182-65962065-f663-483b-abeb-2c8758e116c6.png](./img/DaV-vhgy2BzJ45hC/1686038259182-65962065-f663-483b-abeb-2c8758e116c6-244568.png)

        或者在状态栏上右击，打开“设置当前编码配置”，打开配置界面，选择全局设置，对快捷键进行配置。

![1698282556222-80c21847-0192-43ea-8c96-5d61baad88e3.png](./img/DaV-vhgy2BzJ45hC/1698282556222-80c21847-0192-43ea-8c96-5d61baad88e3-467102.png)

1. 《快捷键只在候选窗口显示情况下才起作用吗？》，如果选择是，则只有当候选框显示的时候按快捷键组合，才会起作用。
2. 《获取使用说明重新配置快捷》，当对某些快捷键进行自定义修改后，请对“<font style="color:rgb(38, 38, 38);">keg.txt</font>”进行保存，接着按此快捷键（默认为<font style="color:#DF2A3F;">左Ctrl+Home+左Win</font>键），则不用重启小科狗服务，快捷键即时生效。
3. 《在线查找快捷》，此快捷键默认为<font style="color:#DF2A3F;">F5</font>或<font style="color:#DF2A3F;">左Ctrl+F5</font>，针对功能为“<font style="color:rgb(38, 38, 38);">keg.txt</font>”里面的在线查找功能，详见<font style="color:#DF2A3F;">3.15.1</font>节。
4. 《方案选择快捷》，即为方案切换快捷键。默认有四个：<font style="color:#DF2A3F;">左Ctrl+F4</font>、<font style="color:#DF2A3F;">F4</font>、<font style="color:#DF2A3F;">左Ctrl+~</font>（tab上面的键），<font style="color:#DF2A3F;">左Ctrl+\</font>。可根据需要，保留习惯用的按键，不需要的删除即可。在进行方案选择时，利用**<font style="color:#E4495B;">上下箭头</font>**选中需要的方案，按**<font style="color:#E4495B;">空格键</font>**确认切换，**<font style="color:#DF2A3F;">按回车则直接上屏方案名</font>**。
5. <font style="color:rgb(38, 38, 38);">《打开配置文字配置界面快捷》，默认</font><font style="color:#DF2A3F;">左Ctrl+左Shift+Enter</font>
6. 《候选窗口调大/小快捷》，默认为<font style="color:#DF2A3F;">左Ctrl+上下箭头</font>或者<font style="color:#DF2A3F;">左Ctrl+小键区加减号</font>，**<font style="color:#DF2A3F;">候选框调整到最小状态时，自动隐藏，达到盲打效果。</font>**
7. 《手动加词条快捷》：<font style="color:#DF2A3F;">左Ctrl+Ins</font>
8. 《上屏后再取坐标快捷》：<font style="color:#DF2A3F;">左Ctrl+左Shift+BackSpace</font>，请自行尝试该功能的作用。
9. 《显示或隐藏状态条快捷》：<font style="color:#DF2A3F;">左Ctrl+F1</font>
10. 《截图候选窗口上屏快捷》，即为整个候选框截图上屏，<font style="color:#DF2A3F;">右Ctrl+Enter</font>
11. 《截图选中候选上屏快捷》，即候选框中选中项直接单字截图上屏，<font style="color:#DF2A3F;">左Ctrl+k</font>
12. 《截图选中反查上屏快捷》，即候选框中选中项的反查内容截图上屏，<font style="color:#DF2A3F;">左Ctrl+l</font>，必须要有反查内容显示才会起作用。
13. 《设置截候选图上屏快捷》，即开启后，上屏内容均会截图上屏，而非纯文字，<font style="color:#DF2A3F;">左Ctrl+左Win+]</font>
14. <font style="color:#000000;">《上屏反查字段快捷》，即直接上屏反查纯文字而非截图内容，</font><font style="color:#DF2A3F;">左Ctrl+BaceSpace</font><font style="color:#000000;">，必须要有反查内容显示才会起作用。</font>
15. <font style="color:#000000;">《显示或隐藏反查字段快捷》，即为显示或隐藏反查内容，</font><font style="color:#DF2A3F;">左Ctrl+j</font><font style="color:#000000;">，可配合12及14使用。</font>
16. <font style="color:#000000;">《删除方案或词条快捷》，左Ctrl+Delete，呼出方案选择界面或候选框，按此快捷键，即可删除对应方案或候选框。</font>
17. 手动调频：Ctrl+对应候选项序号（<font style="color:red;">要看到手动调频效果，请将《候选词条要按调频权重捡索排序吗?=要》改为要</font>）。
18. 《调整词置顶快捷》，左Ctrl+Home，在候选框利用上下箭头调整到需要调频顺序的候选顶上按此快捷键，则选中项一次到顶。
19. 模式切换：小科狗模式：Ctrl+F8，大科狗模式：Ctrl+F9，小狼狗模式：Ctrl+F10
20. 临时英文长句功能：按住Shift输入某一个英文单词时，进入临时英文长句状态。
21. 调试功能：在候选框显示的情况下，同时按ctrl+⬅️+➡️，或者按ctrl+➡️+小键盘数字0，打开小科狗调试功能，注意，此功能是在开发者需要进行相关功能优化调试时使用，一般不用打开。
22. 状态栏皮肤选择：左ctrl+数字键盘点号键。

**<font style="color:#DF2A3F;">注意：</font>**同一个按键，其功能起作用的顺序为： <font style="color:#E4495B;">选重优先级最高，如果不设选重则标点顶屏</font><font style="color:#DF2A3F;">起作用</font>。

## 快捷键的自定义
**<font style="color:#DF2A3F;">小科狗输入法平台所有快捷键均可自定义</font>****，**自定义代码如下表所示，如想替换“<font style="color:rgb(38, 38, 38);">keg.txt</font>”中的默认快捷键，根据下表替换即。

表1 快捷键对应表

| 序号 | 键名 | 代码 |
| :---: | :---: | :---: |
| 1 | 回车键(Enter) | ENT |
| 2 | 上翻页键<font style="color:rgb(38, 38, 38);">(PageUp)</font> | PU |
| 3 | 下翻页键<font style="color:rgb(38, 38, 38);">(PageDown)</font> | PD |
| 4 | 左Shift | LSH |
| 5 | 右Shift | RSH |
| 6 | 左Ctrl | LCT |
| 7 | 右Ctrl | RCT |
| 8 | 向上箭头<font style="color:rgb(38, 38, 38);">(↑)</font> | UP |
| 9 | 向下箭头<font style="color:rgb(38, 38, 38);">(↓)</font> | DOW |
| 10 | 向左箭头<font style="color:rgb(38, 38, 38);">(←)</font> | LEF |
| 11 | 向右箭头<font style="color:rgb(38, 38, 38);">(→)</font> | RIG |
| 12 | Home键 | HOM |
| 13 | 左Win键 | LWI |
| 14 | 右Win键 | RWI |
| 15 | 回退键(BackSpace) | BAC |
| 16 | 删除键(Delete) | DEL |
| 17 | 插入键(Insert) | INS |
| 18 | 大写CapsLock | CAP |
| 19 | End键 | END |


注意，目前小科狗输入法平台快捷键不支持<font style="color:#DF2A3F;">Alt</font>键。

快捷键组合不能单独使用Ctrl和Shift组合，否则无效。

如果是小键盘区键盘，请前面加小写字母<font style="color:#DF2A3F;">x</font>，例如按小键盘区“+”为<font style="color:rgb(38, 38, 38);">“</font>x+<font style="color:rgb(38, 38, 38);">”</font>，<font style="color:rgb(38, 38, 38);">“</font>-<font style="color:rgb(38, 38, 38);">”</font>为<font style="color:rgb(38, 38, 38);">“</font>x-<font style="color:rgb(38, 38, 38);">”。</font>

F1-F12键可以单独使用，其它截图快捷键只能和Ctrl组合使用。

**<font style="color:#DF2A3F;">注意：</font>**小科狗同一个快捷键最多支持四个按键组合（一个尖括号<>里面放一个快捷键），并且同一个快捷键组合里面，只能有<font style="color:#DF2A3F;">一个</font>字母且字母均为**<font style="color:#DF2A3F;">小写</font>**，两个及以上会并击，进而导致快捷键功能失效。

  




# 第一章 快速入门
## 1.1 下载、解压并安装
由于小科狗目前无官方网站，如要下载安装小科狗输入法平台，选择以下两种方式之一下载：

> 1）小科狗官方QQ群（641858743）：群文件 → “小科狗输入法”文件夹，下载“小科狗.7z”。
>
> 2）永硕E盘（[http://kegou.ysepan.com/](http://kegou.ysepan.com/)）：“小科狗输入法”文件夹，下载最新版本安装包（请根据后面显示的日期下载最新版）（<font style="color:rgb(38, 38, 38);">下图</font>）。
>

![1682305361271-803f2b1f-b115-4107-9fa3-1daaabe7380a.png](./img/DaV-vhgy2BzJ45hC/1682305361271-803f2b1f-b115-4107-9fa3-1daaabe7380a-251255.png)

**小科狗的「安装」过程有些特别。**普通软件运行下载的安装程序，点击「下一步」就能傻瓜式完成安装。但小科狗在某些方面像绿色软件，要把 7z 压缩包的内容**手动解压**出来；进行必要的**手动权限准备**；最后<font style="color:rgb(38, 38, 38);">运行其中的</font>**<font style="color:rgb(38, 38, 38);">安装引导程序</font>**<font style="color:rgb(38, 38, 38);">。请仔细阅读压缩包中的《小科狗输入法平台安装卸载说明.docx》，并辅助阅读如下补充说明：</font>

**<font style="color:rgb(38, 38, 38);">①手动解压</font>**：建议把 小科狗.7z 的内容，全部解压到 c:\Program Files (x86)\小科狗 目录。

**<font style="color:rgb(38, 38, 38);">②手动权限、文件准备</font>**：

    - 对 <font style="color:rgb(38, 38, 38);">KegServer.exe，以及 SiKegSetup-32bit.exe / SiKegSetup-64bit.exe ，设定管理员权限。具体方法是：选中文件，右击打开属性，打开“兼容性”，勾选“以管理员身份运行此程序”。</font>
    - <font style="color:rgb(38, 38, 38);">如果安装在C盘，还建议把压缩包中的 </font>**<font style="color:#4874cb;">Keg.db文件、Keg.txt文件以及car文件夹和zj文件夹复制到C:\SiKegInput文件夹里面，使之避开特殊文件夹的读写权限。</font>**
    - <font style="color:rgb(38, 38, 38);">如果你需要的不是现代五笔，而是86五笔等，也建议在此步骤替换db（详见 1.3 输入法方案的选择）</font>

选择安装文件中的 KegServer.exe，右击打开属性，打开“兼容性”，勾选“以管理员身份运行此程序”<font style="color:#DF2A3F;">，打开“数字签名”，选择签名列表中的签名，点击“详细信息”，打开“数字签名详细信息”，如果数字签名信息下面显示为签名不正常，则点击下方的“查看证书”，打开“证书”界面，选择其中的“安装证书”，将证书安装到“受信任的根证书颁发机构”即可（下图）。（红色文字可能已经过时）</font>

![1653658394603-85093b4e-bc82-4ddd-9d68-f501a469ef71.png](./img/DaV-vhgy2BzJ45hC/1653658394603-85093b4e-bc82-4ddd-9d68-f501a469ef71-018892.png)![1653658398157-9981aca0-022f-4cfa-a968-2b54b7875e21.png](./img/DaV-vhgy2BzJ45hC/1653658398157-9981aca0-022f-4cfa-a968-2b54b7875e21-381961.png)

![1654507154878-1c53b1b3-8af8-455e-b257-96089f91e886.png](./img/DaV-vhgy2BzJ45hC/1654507154878-1c53b1b3-8af8-455e-b257-96089f91e886-251520.png)

![1653792396397-69bd9fc1-4ce4-44bc-890e-02bc6649f48b.png](./img/DaV-vhgy2BzJ45hC/1653792396397-69bd9fc1-4ce4-44bc-890e-02bc6649f48b-661114.png)

![1653792483494-98bf7af3-5ac1-45e8-b42b-c3bc00705759.png](./img/DaV-vhgy2BzJ45hC/1653792483494-98bf7af3-5ac1-45e8-b42b-c3bc00705759-170472.png)

**③安装：**根据电脑操作系统的不同，双击 SiKegSetup-32bit.exe 或者 SiKegSetup-64bit.exe 运行安装程序，（稳妥起见，建议右键「管理员权限运行」）在弹出的对话框中选择点击“安装”按钮进行安装~~<font style="color:#DF2A3F;">，随后两个“tsf注册”弹出框同样选择确定</font>~~，最后提示小科狗安装成功。

![1653792717593-d5cdd6e5-e7a4-4c1f-bf07-66c5495c5d58.png](./img/DaV-vhgy2BzJ45hC/1653792717593-d5cdd6e5-e7a4-4c1f-bf07-66c5495c5d58-999691.png)

![1660122679797-dd7a1ddc-da17-40f7-86c7-661a6aaadad6.gif](./img/DaV-vhgy2BzJ45hC/1660122679797-dd7a1ddc-da17-40f7-86c7-661a6aaadad6-365861.gif)

## 1.2 小科狗卸载
如果要卸载小科狗，同样打开安装时的文件，点击“卸载”按钮即可。

![1685327134427-f1eb43e6-6561-4ed7-b616-cb2f25433074.png](./img/DaV-vhgy2BzJ45hC/1685327134427-f1eb43e6-6561-4ed7-b616-cb2f25433074-787405.png)

卸载过程中，可能会弹出安全提示，接受即可。程序会调用 remove.bat ，弹出命令行窗口，执行相应的卸载动作。

![1747633449338-b6079c01-7e51-4041-8ded-016ed6982463.png](./img/DaV-vhgy2BzJ45hC/1747633449338-b6079c01-7e51-4041-8ded-016ed6982463-855502.png)

## 1.3 输入法方案的选择
小科狗输入法平台，支持多种输入法方案。

所有输入法方案统一保存在安装目录下的 Keg.db 数据文件中。

注意：安装包自带的 <font style="color:rgb(38, 38, 38);">Keg.db（约 4.8MB） </font>仅有“现代五笔带拼音”和“现代五笔无重GB字集编码”2种方案，不包括“86五笔”等方案。如需要其他输入方案（大概率如此），可到 QQ 群文件，“小科狗输入法码表”中下载完整 Keg.db（自带几十种不同码表，约 148MB），替换安装文件中的码表即可。

<font style="color:red;">注意：如果小科狗已经安装并使用，替换时请停止服务，待替换完成后再重新启动服务，</font><font style="color:black;">具体操作为显示候选框，右击选择「退出服务端」</font>。（退出服务端后，如果继续击键，输入法会自动重新启动服务端。）

![1653792894804-b0f55911-b1f8-4b3c-928b-449f8d109455.png](./img/DaV-vhgy2BzJ45hC/1653792894804-b0f55911-b1f8-4b3c-928b-449f8d109455-145323.png)



![1682307761821-8e27992c-a447-4b42-a7d6-65c0807d1ee7.png](./img/DaV-vhgy2BzJ45hC/1682307761821-8e27992c-a447-4b42-a7d6-65c0807d1ee7-851005.png)



如何切换不同输入法方案？小科狗预置了四种切换热键：F4、Ctrl+F4、Ctrl+~、Ctrl+\。

和其他热键一样，上述热键的查看和修改，可以编辑  <font style="color:rgb(38, 38, 38);">keg.txt 文档来实现。用户</font>可以根据自己的使用习惯，选择保留合适的切换方案，如果觉得哪个快捷键和其它软件相冲突，直接删除即可。<font style="color:red;">注意：①只可使用以上四个快捷键的某一个、几个或全部；②用户也可自定义其他热键；③如果全部删除，则无快捷键可用，导致无法打开方案切换界面。修改 reg.txt 后，保存文档，重启小科狗服务即可生效。</font>

## 1.4 码表方案的选择
小科狗输入法平台目前共内置了超过20种拼音、形码和英文等输入法方案，利用方案切换快捷键直接呼出方案选择菜单，利用上下箭头移动光标，选择需要的方案，按**<font style="color:#E4495B;">空格键</font>**确认（按回车键，可上屏方案名称），即切换至该输入法方案。

![1653658498440-e9d46651-6213-4341-a8a4-a0a05db5f069.png](./img/DaV-vhgy2BzJ45hC/1653658498440-e9d46651-6213-4341-a8a4-a0a05db5f069-759503.png)



此时即可以用所选择的码表方案进行文字输入了，至于输入习惯的设置，请看第二章：快速设置输入法习惯的。

---



# 第二章 小科狗其它功能的配置
## 2.1 候选界面及状态栏设置
### 2.1.1 候选界面设置
小科狗输入法平台每个码表候选界面除了可通过小科狗配置助手内置配色方案进行选择快速设置外（**<font style="color:#E8323C;">3.1 候选框界面配置</font>**），还可利用小科狗内置的纯文本设置界面进行更加具体的设置。

        请按Ctrl+Shift切换至小科狗输入法，按任意按键呼出小科狗候选项界面，该候选界面默如下图显示，如果要对候选界面进行相关设置，请浏览以下说明。

![1653658763373-085d9c83-f14d-4084-abcb-064fa9b1b2f7.png](./img/DaV-vhgy2BzJ45hC/1653658763373-085d9c83-f14d-4084-abcb-064fa9b1b2f7-324508.png)

在候选界面上右击，选择“设置当前编码方案配置”（下图）或者直接按Ctrl+Shift+空格，进入小科狗方案配置界面：

![1683291121091-5394f4ae-0829-4b7c-b27d-fc08640b2aca.png](./img/DaV-vhgy2BzJ45hC/1683291121091-5394f4ae-0829-4b7c-b27d-fc08640b2aca-984159.png)

![1683291135904-9bca02c9-cc88-45aa-83fb-4f699c3aa7d7.png](./img/DaV-vhgy2BzJ45hC/1683291135904-9bca02c9-cc88-45aa-83fb-4f699c3aa7d7-800321.png)

通过对其中“《候选窗口设置》”下面的相关选择进行设置（下图），以改变候选窗口的样式（<font style="color:#DF2A3F;">字体设置另设一节进行具体介绍</font>）。

![1683291049490-ab76b214-2c7a-4db8-a915-02d5d1f2f50c.png](./img/DaV-vhgy2BzJ45hC/1683291049490-ab76b214-2c7a-4db8-a915-02d5d1f2f50c-588390.png)

其中，候选窗口的设置主要包括以下几个方面：

> 1).  候选个数。候选界面显示的候选项数量，其值为1-26，根据自己的需求自行设置。
>
> 2). 《候选的高度间距=0》和《候选的宽度间距=0》。设置候选框横向或者竖向各个候选项的间隔距离。
>
> 3).  候选窗口横向、竖向设置。候选界面是横向还是竖向显示，默认为横向，如果竖向显示，则将“要”改为“不要”即可。
>
> 4).  《竖向候选窗口选中背景颜色是否等宽》。如下图所示，绿色底色即为选中背景颜色，如果选择等宽，则如左图所示，如果将“要”改为“不要”，则不等宽，效果如右图所示。
>
> ![1653658812457-c4db24d5-45a9-42d7-a7c8-deabf2cfb64e.png](./img/DaV-vhgy2BzJ45hC/1653658812457-c4db24d5-45a9-42d7-a7c8-deabf2cfb64e-923788.png)![1653658815732-245a6928-bb47-4711-9dd7-c24f02661fdf.png](./img/DaV-vhgy2BzJ45hC/1653658815732-245a6928-bb47-4711-9dd7-c24f02661fdf-082399.png)
>

> 5)．《要显示背景图吗》。默认为不要，如果将“不要”改为“要”，则效果如下图所示，背景图为带竹子的背景效果，当然也可以在安装解压目录将keg.bmp图片自定义为自己想要显示的图片。
>
> ![1653658824775-bba66b7c-a9f4-4f89-9b6f-3272bf1b63d6.png](./img/DaV-vhgy2BzJ45hC/1653658824775-bba66b7c-a9f4-4f89-9b6f-3272bf1b63d6-974436.png)
>

> 6)．《高度宽度要完全自动调整吗？》。默认为“要”，则会根据候选界面字词的长短及候选项数量的变化自动调整长度和宽度，如果将“不要”改为“要”，则不会自动调整。请设置为“要”或者“不要”自行体验。
>
> 7)．《窗口四个角要圆角吗？》。默认为“不要”，如果设置为“要”，则四个角会呈倒圆角（下图）。
>
> ![1653658835935-3ed8e543-2dae-4079-bb43-1469bfe00c24.png](./img/DaV-vhgy2BzJ45hC/1653658835935-3ed8e543-2dae-4079-bb43-1469bfe00c24-732322.png)
>

> 8)．候选窗口颜色的调整。此下各项均为候选窗口颜色配置项，不再一一列出。请使用合适的取色工具，对相关颜色进行具体的设置。本说明将个别配色方案附录于下，供大家尝鲜之用。**<font style="color:#AD1A2B;">在使用时，直接复制相关配色方案，点击码表配置界面上方的清空按钮，然后在配置窗口直接粘贴，点击上面的应用-确定，即可将所选配色方案设置成功</font>**（下图）。
>

![1683291578032-08aa8d7e-57b5-4f21-be74-4e33b18a3948.png](./img/DaV-vhgy2BzJ45hC/1683291578032-08aa8d7e-57b5-4f21-be74-4e33b18a3948-098195.png)

小科狗候选框皮肤配色一览表

| 名称 | 代码 | 效果 |
| --- | --- | :---: |
| 微<br/>信<br/>键<br/>盘 | 《候选窗口边框色=(222,222,222)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(35,173,120)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(245,245,245)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(255,255,255)》:0-255,(红,绿,蓝)<br/>《背景底色=(245,245,245)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(4,4,4)><1=(4,4,4)><2=(4,4,4)><3=(4,4,4)><4=(4,4,4)><5=(4,4,4)><6=(4,4,4)><7=(4,4,4)><8=(4,4,4)><9=(4,4,4)><10=(4,4,4)><11=(4,4,4)><12=(4,4,4)><13=(4,4,4)><14=(4,4,4)><15=(4,4,4)><16=(4,4,4)><17=(4,4,4)><18=(4,4,4)><19=(4,4,4)><20=(4,4,4)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839036787-37dcc155-2704-49cd-91f6-c7010c36d808.png](./img/DaV-vhgy2BzJ45hC/1710839036787-37dcc155-2704-49cd-91f6-c7010c36d808-374313.png) |
| 谷<br/>歌 | 《候选窗口边框色=(222,222,222)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(57,116,206)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(245,245,245)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(189,189,189)》:0-255,(红,绿,蓝)<br/>《背景底色=(245,245,245)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(4,4,4)><1=(4,4,4)><2=(4,4,4)><3=(4,4,4)><4=(4,4,4)><5=(4,4,4)><6=(4,4,4)><7=(4,4,4)><8=(4,4,4)><9=(4,4,4)><10=(4,4,4)><11=(4,4,4)><12=(4,4,4)><13=(4,4,4)><14=(4,4,4)><15=(4,4,4)><16=(4,4,4)><17=(4,4,4)><18=(4,4,4)><19=(4,4,4)><20=(4,4,4)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839183454-323d4a60-e4c9-4f7c-9ae9-9ab3542230b5.png](./img/DaV-vhgy2BzJ45hC/1710839183454-323d4a60-e4c9-4f7c-9ae9-9ab3542230b5-513180.png) |
| 致<br/>青<br/>春 | 《候选窗口边框色=(238,232,213)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=4》:1-6<br/>《候选选中色=(42,161,152)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(238,232,213)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(224,224,224)》:0-255,(红,绿,蓝)<br/>《背景底色=(253,246,227)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(211,54,130)><1=(0,0,0)><2=(101,123,131)><3=(101,123,131)><4=(101,123,131)><5=(101,123,131)><6=(101,123,131)><7=(101,123,131)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839223925-a3616331-6925-428f-881a-4b20300cdbe6.png](./img/DaV-vhgy2BzJ45hC/1710839223925-a3616331-6925-428f-881a-4b20300cdbe6-876260.png) |
| 水<br/>果 | 《候选窗口边框色=(195, 239, 60)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=4》:1-6<br/>《候选选中色=(255,255,255)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(43,174,133)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(43,174,133)》:0-255,(红,绿,蓝)<br/>《背景底色=(255,255,255)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(43,174,133)><1=(251,185,87)><2=(251,185,87)><3=(251,185,87)><4=(251,185,87)><5=(251,185,87)><6=(251,185,87)><7=(251,185,87)><8=(251,185,87)><9=(251,185,87)><10=(251,185,87)><11=(0,0,0)><12=(0,0,0)><13=(251,185,87)><14=(0,0,0)><15=(0,0,0)><16=(0,0,0)><17=(0,0,0)><18=(0,0,0)><19=(0,0,0)><20=(0,0,0)><21=(0,0,0)><22=(0,0,0)><23=(0,0,0)><24=(0,0,0)><25=(0,0,0)><26=(0,0,0)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(43,174,133)》:0-255,(红,绿,蓝) | ![1710839746408-5f469e92-0455-4cc3-8be3-ae4401ff56d1.png](./img/DaV-vhgy2BzJ45hC/1710839746408-5f469e92-0455-4cc3-8be3-ae4401ff56d1-372158.png) |
| 黑<br/>黄<br/>水<br/>果 | 《候选窗口边框色=(249,150,0)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-10<br/>《候选选中色=(249,150,0)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(255,255,255)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(0,0,255)》:0-255,(红,绿,蓝)<br/>《背景底色=(0,0,0)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(255,165,0)><1=(255,165,0)><2=(255,165,0)><3=(255,165,0)><4=(255,165,0)><5=(255,165,0)><6=(255,165,0)><7=(255,165,0)><8=(255,165,0)><9=(255,165,0)><10=(255,165,0)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(255,0,0)》:0-255,(红,绿,蓝) | ![1710839934650-041fe48c-fed4-4a4a-8bb8-4437bf999d03.png](./img/DaV-vhgy2BzJ45hC/1710839934650-041fe48c-fed4-4a4a-8bb8-4437bf999d03-917870.png) |
| 蓝<br/>天<br/>白<br/>云 | 《候选窗口边框色=(0,102,204)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-10<br/>《候选选中色=(0,102,204)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(255,255,255)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(0,102,204)》:0-255,(红,绿,蓝)<br/>《背景底色=(255,255,255)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(0,102,204)><1=(0,102,204)><2=(0,102,204)><3=(0,102,204)><4=(0,102,204)><5=(0,102,204)><6=(0,102,204)><7=(0,102,204)><8=(0,102,204)><9=(0,102,204)><10=(0,102,204)><11=(0,0,0)><12=(0,0,0)><13=(0,0,0)><14=(0,0,0)><15=(0,0,0)><16=(0,0,0)><17=(0,0,0)><18=(0,0,0)><19=(0,0,0)><20=(0,0,0)><21=(0,0,0)><22=(0,0,0)><23=(0,0,0)><24=(0,0,0)><25=(0,0,0)><26=(0,0,0)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(0,102,204)》:0-255,(红,绿,蓝) | ![1710840014557-21c3a076-1cbd-494e-b1e6-a0c89bcfd856.png](./img/DaV-vhgy2BzJ45hC/1710840014557-21c3a076-1cbd-494e-b1e6-a0c89bcfd856-346607.png) |
| 孤<br/>寺 | <font style="color:rgb(38, 38, 38);">《候选窗口边框色=(67,67,67)》:0-255,(红,绿,蓝)    </font><font style="color:rgb(38, 38, 38);">《候选选中色=(0,191,217)》:0-255,(红,绿,蓝)    </font><font style="color:rgb(38, 38, 38);">《分隔线色=(67,67,67)》:0-255,(红,绿,蓝)    </font><font style="color:rgb(38, 38, 38);">《背景底色=(67,67,67)》:不设238,232,213就是对话框背景色。0-255,(红,绿,蓝)    </font><font style="color:rgb(38, 38, 38);">《要显示背景图吗？=不要》:    </font><font style="color:rgb(38, 38, 38);">《高度宽度要完全自动调整吗？=要》:窗口大小随候选变化   </font><font style="color:rgb(38, 38, 38);">《窗口四个角要圆角吗？=不要》:    </font><font style="color:rgb(38, 38, 38);">《候选字体色串=<0=(238,240,235)><1=(238,240,235)><2=(238,240,235)><3=(238,240,235)><4=(238,240,235)><5=(238,240,235)><6=(238,240,235)><7=(238,240,235)>》:0代表编码串，1代表候选1，以此类推</font> | ![1653658902731-893fa1e1-7ea5-4032-aabf-7c3e55986fb4.png](./img/DaV-vhgy2BzJ45hC/1653658902731-893fa1e1-7ea5-4032-aabf-7c3e55986fb4-685184.png) |
| 鲸<br/>鱼 | 《候选窗口边框色=(94,105,126)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(102,132,196)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(230,216,202)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(145,167,212)》:0-255,(红,绿,蓝)<br/>《背景底色=(145,167,212)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(10,74,123)><1=(10,74,123)><2=(10,74,123)><3=(10,74,123)><4=(10,74,123)><5=(10,74,123)><6=(10,74,123)><7=(10,74,123)><8=(10,74,123)><9=(10,74,123)><10=(10,74,123)>:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839255409-4469ed7a-5cab-4990-bfa0-23bd2de02e8f.png](./img/DaV-vhgy2BzJ45hC/1710839255409-4469ed7a-5cab-4990-bfa0-23bd2de02e8f-015874.png) |
| 魅<br/>蓝 | 《候选窗口边框色=(94,105,126)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(98,50,199)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(255,255,255)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(145,167,212)》:0-255,(红,绿,蓝)<br/>《背景底色=(43,44,60)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(128,130,212)><1=(128,130,212)><2=(128,130,2123)><3=(128,130,212)><4=(128,130,212)><5=(128,130,212)><6=(128,130,212)><7=(128,130,212)><8=(128,130,212)><9=(128,130,212)><10=(128,130,212)> >》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839271416-5ed288aa-a4d6-40fc-ab6b-5c53e07358d1.png](./img/DaV-vhgy2BzJ45hC/1710839271416-5ed288aa-a4d6-40fc-ab6b-5c53e07358d1-740591.png) |
| 仙<br/>人<br/>掌 | 《候选窗口边框色=(70,146,104)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(149,205,178)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(205,97,147)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(145,167,212)》:0-255,(红,绿,蓝)<br/>《背景底色=(202,231,213)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(40,128,80)><1=(40,128,80)><2=(40,128,80)><3=(40,128,80)><4=(40,128,80)><5=(40,128,80)><6=(40,128,80)><7=(40,128,80)><8=(40,128,80)><9=(40,128,80)><10=(40,128,80)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839286101-c8d34127-8e00-4f15-893b-be2c9e2bf96d.png](./img/DaV-vhgy2BzJ45hC/1710839286101-c8d34127-8e00-4f15-893b-be2c9e2bf96d-719353.png) |
| 松<br/>鹤 | 《候选窗口边框色=(70,146,104)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(215,217,192)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(55,55,44)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(145,167,212)》:0-255,(红,绿,蓝)<br/>《背景底色=(96,113,78)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(55,55,44)><1=(205,207,179)><2=(205,207,179)><3=(205,207,179)><4=(205,207,179)><5=(205,207,179)><6=(205,207,179)><7=(205,207,179)><8=(205,207,179)><9=(205,207,179)><10=(205,207,179)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839300779-b5fc9931-9b31-4489-85c4-bc12b8ffe209.png](./img/DaV-vhgy2BzJ45hC/1710839300779-b5fc9931-9b31-4489-85c4-bc12b8ffe209-614576.png) |
| 樱<br/>花 | 《候选窗口边框色=(223,139,154)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(240,190,196)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(121,45,54)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(145,167,212)》:0-255,(红,绿,蓝)<br/>《背景底色=(246,225,235)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(213,105,119)><1=(213,105,119)><2=(213,105,119)><3=(213,105,119)><4=(213,105,119)><5=(213,105,119)><6=(213,105,119)><7=(213,105,119)><8=(213,105,119)><9=(213,105,119)><10=(213,105,119)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839317440-d8c84a5d-6dff-4fc8-9d17-3e7025433b74.png](./img/DaV-vhgy2BzJ45hC/1710839317440-d8c84a5d-6dff-4fc8-9d17-3e7025433b74-971849.png) |
| 饼<br/>干 | 《候选窗口边框色=(26,25,25)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(216,153,91)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(0,0,0)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(254,210,153)》:0-255,(红,绿,蓝)<br/>《背景底色=(254,210,153)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(0,0,0)><1=(148,107,64)><2=(148,107,64)><3=(148,107,64)><4=(148,107,64)><5=(148,107,64)><6=(148,107,64)><7=(148,107,64)><8=(148,107,64)><9=(148,107,64)><10=(148,107,64)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839333090-9e937566-c028-46ec-9138-5f25eed853f6.png](./img/DaV-vhgy2BzJ45hC/1710839333090-9e937566-c028-46ec-9138-5f25eed853f6-822557.png) |
| 咏<br/>竹 | 《候选窗口边框色=(203,221,191)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(128,154,111)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(0,0,0)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(254,210,153)》:0-255,(红,绿,蓝)<br/>《背景底色=(241,236,207)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(0,0,0)><1=(97,132,85)><2=(97,132,85)><3=(97,132,85)><4=(97,132,85)><5=(97,132,85)><6=(97,132,85)><7=(97,132,85)><8=(97,132,85)><9=(97,132,85)><10=(97,132,85)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839350507-50622c2e-fd5d-4062-9f77-b97be5149549.png](./img/DaV-vhgy2BzJ45hC/1710839350507-50622c2e-fd5d-4062-9f77-b97be5149549-646346.png) |
| 银<br/>尘 | 《候选窗口边框色=(60,60,60)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(29,29,29)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(255,255,255)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(219,219,219)》:0-255,(红,绿,蓝)<br/>《背景底色=(243,243,243)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(0,0,0)><1=(84,81,100)><2=(84,81,100)><3=(84,81,100)><4=(84,81,100)><5=(84,81,100)><6=(84,81,100)><7=(84,81,100)><8=(84,81,100)><9=(84,81,100)><10=(84,81,100)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839365035-35736add-0494-4215-ae77-c6a99dfa1dad.png](./img/DaV-vhgy2BzJ45hC/1710839365035-35736add-0494-4215-ae77-c6a99dfa1dad-812953.png) |
| 牛<br/>油<br/>果 | 《候选窗口边框色=(101,73,21)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(116,148,92)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(244,234,113)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(219,219,219)》:0-255,(红,绿,蓝)<br/>《背景底色=(201,222,163)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(111,98,38)><1=(111,98,38)><2=(111,98,38)><3=(111,98,38)><4=(111,98,38)><5=(111,98,38)><6=(111,98,38)><7=(111,98,38)><8=(111,98,38)><9=(111,98,38)><10=(111,98,38)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839380468-0f2478d6-a00d-4ade-997c-e9d1cfd0e864.png](./img/DaV-vhgy2BzJ45hC/1710839380468-0f2478d6-a00d-4ade-997c-e9d1cfd0e864-746822.png) |
| 奶<br/>油 | 《候选窗口边框色=(101,73,21)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(22,21,20)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(244,234,113)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(219,219,219)》:0-255,(红,绿,蓝)<br/>《背景底色=(255,253,202)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(178,178,177)><1=(94,94,84)><2=(94,94,84)><3=(94,94,84)><4=(94,94,84)><5=(94,94,84)><6=(94,94,84)><7=(94,94,84)><8=(94,94,84)><9=(94,94,84)><10=(94,94,84)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839393005-eb56f2f6-b3f5-4882-953d-94017e1c26d3.png](./img/DaV-vhgy2BzJ45hC/1710839393005-eb56f2f6-b3f5-4882-953d-94017e1c26d3-164716.png) |
| 宁<br/>静<br/>致<br/>远 | 《候选窗口边框色=(215,186,161)》:0-255,(红,绿,蓝)<br/>《候选窗口边框线宽度=2》:1-6<br/>《候选选中色=(215,186,161)》:选中的背景色，0-255,(红,绿,蓝)<br/>《候选选中字体色=(60,33,37)》:选中的字体色，0-255,(红,绿,蓝)<br/>《分隔线色=(219,219,219)》:0-255,(红,绿,蓝)<br/>《背景底色=(254,245,238)》:不设就是对话框背景色。0-255,(红,绿,蓝)<br/>《候选字体色串=<0=(0,0,0)><1=(119,82,77)><2=(119,82,77)><3=(119,82,77)><4=(119,82,77)><5=(119,82,77)><6=(119,82,77)><7=(119,82,77)><8=(119,82,77)><9=(119,82,77)><10=(119,82,77)>》:0代表编码串，1代表候选1，以此类推<br/>《光标色=(101,123,131)》:0-255,(红,绿,蓝) | ![1710839406778-1a6c6f59-86a5-4ce4-a578-d604f6df9223.png](./img/DaV-vhgy2BzJ45hC/1710839406778-1a6c6f59-86a5-4ce4-a578-d604f6df9223-014340.png) |


**<font style="color:red;">在设置完成后，请选择“保存内存数据库到硬盘”（下图），否则当前设置不会同步到对应码表中去。</font>**

![1710838446651-864d2abb-71c4-46a6-8be1-37808c1c3d00.png](./img/DaV-vhgy2BzJ45hC/1710838446651-864d2abb-71c4-46a6-8be1-37808c1c3d00-437713.png)

### 2.1.2 状态栏配置
小科狗状态栏目前处于试用阶段，状态栏显示具体如下图所示，可以通过鼠标左键单击手动到桌面任意位置放置，包括放置于任务栏上面。

状态栏由三部分构成：最前面的logo，中间的中英文提示（中文状态为黑色字体的“中c”，英文状态为红色字体的“英e”），最后面的码表名。码表名由直接读取当前码表中的码表名，所以请给码表合理命名，太长，则显示不完全，太短，则右边空太多，不美观。

![1686274279290-4ca91636-e7dd-4416-a321-a0edf051b5dd.png](./img/DaV-vhgy2BzJ45hC/1686274279290-4ca91636-e7dd-4416-a321-a0edf051b5dd-933242.png)

如果不想显示状态栏，则在候选框显示的情况下，默认按Ctrl+F1，即可将状态栏隐藏。

请使用时注意，小科狗状态栏码表名称及中英文状态会随着主辅码表、引导码表的切换而实时变换，以达到提示目的。

---

**<font style="color:#DF2A3F;">20231217更新：</font>**

目前小科狗支持静态和动态皮肤配置，设置位置在安装包下的keg.txt文件中的“<font style="color:#DF2A3F;">状态栏</font>”一行。

默认配置快捷键为左ctrl+小键盘点号键。打开后，可选择任意静态图片或者gif动态图，注意，为了显示美观，请使用透明背景图片。

小科狗状态栏目前有以下几个配置项：

![1704284000428-4dbe23e6-18a9-461a-a9fd-5e681cd128af.png](./img/DaV-vhgy2BzJ45hC/1704284000428-4dbe23e6-18a9-461a-a9fd-5e681cd128af-527732.png)

提示文本的位置：状态栏中的文字目前可以设置在三个位置：上对齐、居中、下对齐（见下图）。

提示文本要隐藏吗：选择要，则会显示下图中的：<font style="color:#117CEE;">中c：86五笔</font>提示文字。

提示文本要显示中英以及大小写状态吗：选择要或不要，请自行尝试其不同。

提示文本中/英文字体色：即设置提示文字的颜色。

提示文本字体大小：即设置提示文字的大小。

提示文本字体名称：即设置提示文字的字体。

**<font style="color:#DF2A3F;">注意：</font>**所使用的图片可以放在任意位置，并且任意名称都行。但动态图要是真正的gif图片，有的图片虽然显示的是gif后缀名，但是并非真正的gif图片，选择后导致输入法退出服务。



![1702809643961-eba82714-b297-4bf2-8479-eb645a6c93d7.gif](./img/DaV-vhgy2BzJ45hC/1702809643961-eba82714-b297-4bf2-8479-eb645a6c93d7-323472.gif)

文字在皮肤上部



![1702994253337-c717b6a9-b294-40fa-9cb8-31a9dec419ec.gif](./img/DaV-vhgy2BzJ45hC/1702994253337-c717b6a9-b294-40fa-9cb8-31a9dec419ec-955965.gif)

文字在皮肤中间



![1702994174962-990e1054-2a0e-40bd-876c-8402922e2f60.gif](./img/DaV-vhgy2BzJ45hC/1702994174962-990e1054-2a0e-40bd-876c-8402922e2f60-208605.gif)

文字在皮肤下部



## 2.2 其它输入习惯设置
本设置基于86版五笔个人使用习惯加以说明，不同输入法方案以及个人使用习惯的不同，可能设置有所差异，请依据本说明，酌情进行相关设置。

### 2.2.1 动作设置
动作设置项中包括10项设置，每项具体如下：

> 1).《要显示键首字根吗?=不要》。该项设置主要针对键字根码表，如果value值是以键首字根表示，则建议开启。
>
> 2).《上屏后候选窗口要立即消失吗?=要》，建议设置为要。
>
> 3).《超过码长要清屏吗?=要》，建议设置为要。
>
> 4).《无候选要清屏吗?=要》，建议设置为要。
>
> 5).《要启用最大码长无候选清屏吗?=要》，建议设置为要。
>
> 6).《无候选敲空格要上屏编码串吗?=要》，请自行试验该功能是否要开启。
>
> 7).《Shift键上屏编码串吗?=要》，要则在不上屏候选项的情况下，按shift直接上屏编码串。
>
> 8).《Enter键上屏编码串吗?=要》，要则效果同7）。
>
> 9).《Enter键上屏并使首个字母大写吗?=要》，要是则直接上屏编码串，并且首个字母大写。
>
> 10).《Backspace键一次性删除前次上屏的内容吗?=不要》，如果为要，则Backspace键一次删除上次输入内容，不要是逐字删除。
>

### 2.2.2 重复上屏、码表调频与检索设置
> 1)．码表设置中的《最大码长》。码表设置中的最大码长默认为5，如果为86五笔，最大码长本身为4，则将此处的5改为4，其它输入法根据情况设置。
>
> 2)．重复上屏码元字符串。即为重复上屏快捷键设置，可以根据个人使用习惯设置重复上屏快捷键。例如86版五笔Z键不作用编码码元参与输入，可作用重复上屏键，即在《重复上屏码元字符串=》等号后面写入小写z，此时输入时，如果要上屏前几次输入的内容，则直接按z键，会出现如下图的提示框，如果直接按空格键，则第2个候选项会直接上屏，如果要重复上屏前面的某个候选项，请利用数字键选重上屏。
>
> ![1653659045939-68ed3010-7148-4a92-ad5e-f95bedd64837.png](./img/DaV-vhgy2BzJ45hC/1653659045939-68ed3010-7148-4a92-ad5e-f95bedd64837-142582.png)
>

> 3)．《要逐码提示检索吗?=要》。默认为要，则候选界面不但会显示编码串完全匹配的候选项，也会显示包含输入编码串的候选项，相当于模糊匹配，效果如下图左图所示，如果将“要”改为“不要”，则仅显示编码串完全匹配的候选项，相当于精确匹配，效果如下图右图所示。
>
> ![1653659059811-b37733c9-d8a6-4d20-a4ac-0ab7b2e62a52.png](./img/DaV-vhgy2BzJ45hC/1653659059811-b37733c9-d8a6-4d20-a4ac-0ab7b2e62a52-675305.png)![1653659062580-a03c14b8-1901-4f4b-9566-d14a9a86d5f3.png](./img/DaV-vhgy2BzJ45hC/1653659062580-a03c14b8-1901-4f4b-9566-d14a9a86d5f3-057355.png)
>

> 4)．《要显示逐码提示吗?=要》。默认要，则会显示编码串提示，效果如下图左图，如果将“要”改为“不要”，则不会显示编码串提示，效果如下图右图所示。
>
> ![1653659071986-d152a1c5-1513-4e11-8330-a35749718184.png](./img/DaV-vhgy2BzJ45hC/1653659071986-d152a1c5-1513-4e11-8330-a35749718184-183052.png)![1653659074854-e3ae0ba6-d73c-4a92-b01f-2fc29b7ffd54.png](./img/DaV-vhgy2BzJ45hC/1653659074854-e3ae0ba6-d73c-4a92-b01f-2fc29b7ffd54-432809.png)
>

> 5)．《要显示反查提示吗?=要》。反查提示设置，默认要，如果将“要”改为“不要”，则不会提示反查内容。该设置选项针对形码字根拆分、拼音、注音等反查功能设置，详情请查阅“**<font style="color:#E8323C;">3.4反查功能</font>**”小节。
>
> 6)．《要启用上屏自动增加调频权重吗?=要》。默认为要，即某一字、词输出一次，则其码表中的权重值（weight）加1。
>
> 7)．《要启用上屏自动增加调频权重直接到顶吗?=要》。默认为要，只有启用了上屏自动增加调频权重此项才会生效。此功能开启时，输入某一字词，则该字词会自动调频到第一候选项。相当于其它输入法的**<font style="color:red;">一次到顶</font>**功能。
>
> 8)．《候选词条要按码长短优先排序吗?=要》。默认为要，如果开启了自动调频，则必须将此项设置为要，不然检索混乱。
>
> 9)．《候选词条要按调频权重捡索排序吗?=要》。默认为要，开启是，则候选字词会按调频的权重值进行重新排序，而不是按字词前面的序列号进行排序。
>
> 10)．《候选快键字符串=<1=><2=><3=>》，默认没有设置候选项选重键。小科狗选重数最多可支持26个，支持47个符号键位和空格等4个无符号键位，有效键位共55个。如果要设置选重键，则将对应按键录入到每个等号之后，例如使用分号键选择第2个候选项，单引号选择第3个候选项，则其设置为：《候选快键字符串=<1=><2=;><3='>》，以此类推。<font style="color:red;">需要注意的是，选重键必须为英文小写字符，否则不会起作用，</font><font style="color:#E8323C;">左Shift用ls、右Shift用rs、左ctrl用lc、右ctrl用rc、</font><font style="color:red;">空格键用sp、回车键用en、Tab键用ta、CapsLock键用ca代替，例如<1=\en><2=;><3='><4=,.>等。并且同一候选同时支持多个选重按键选重，如前面第1个候选选重键为“\en”，即按\或者回车键均可上屏第1个候选项，第4个候选选重键为“,.”，即表示按逗号或者句号均可上屏第4个候选项，如果改为<4=,.ta>，即表示按逗号或句号或tab键均可上屏第4候选项。</font>
>
> 11)．《要码长顶屏吗?=不要》。默认为不要，如果想要码长顶屏，则设置为要。
>
> 12)．《唯一上屏最小码长=0》。默认为0，例如86五笔最大码长为4，则在此设置了4。
>
> 13)．《要唯一上屏吗?=要》。默认为要，如果候选项唯一，则会直接上屏。
>
> 14)．《要标点顶屏吗?=不要》。默认不要。如果将“不要”改为“要”，则在输入完一字词时，没有直接上屏，如果此时输入标点符号，则第一个候选项会直接上屏。
>
> 15)．《要数字顶屏吗?=不要》。默认不要。如果为要，则会开启数字顶屏，其效果和标点顶屏相同，具体请看**<font style="color:#E8323C;">3.10 顶功功能</font>**<font style="color:black;">一节。</font>
>
> 16)．《要启用单字模式吗?=不要》。该功能针对单字输入派。如果为不要，则单字和词组均可输入，如果为要，则只能进行单字输入，而无法输入词组。
>
> 17)．《翻页键》。小科狗提供了自定义翻页键，可以对主键盘区和数字键区分别进行自定义。请在翻页键选项下根据自己的使用习惯进行尝试设置试用即可。
>
> 18)．小科狗内置两种通讯方式：命名管道和消息共享内存，可通过F8快捷键进行切换，会有弹出框提示，请自行设置体验这两种方式的不同之处。
>
> 19)．候选界面字体大小调整。小科狗候选界面字体大小的调整通过快捷键实现：Ctrl+上下箭头。Ctrl+↑调大字体，Ctrl+↓调小字体，请自行使用体验。
>

### 2.2.3 字体设置（回退、绘制等设置）
小科狗输入法平台，可以针对每个编码方案单独设置候选框显示字体，字体设置时，可分为两个大的板块进行设置，其一是字体的显示设置，其二是Emoji符号的显示设置，但是在设置时，两个是可以同时设置、合二为一的，具体设置见以下说明。

![1681651403303-18bd8ba9-791b-4946-bef1-44822ef22a17.png](./img/DaV-vhgy2BzJ45hC/1681651403303-18bd8ba9-791b-4946-bef1-44822ef22a17-533646.png)

小科狗输入法平台能完美显示生僻字、私有区字、Emoji符号等。无论是何种码表导入到软件后，请同时按“Ctrl+Shift+空格”打开配置界面，候选框字体的具体设置见上图红色框内所示。

1. 《字体名称》为主字体名称，即码表所用的主体字体，当候选框显示候选项时，首先调用该字体显示。
2. 《D2D回退字体集》为回退字体名称，该设置可以设置N个字体，中间用+号隔开，其原理为，当主字体无法显示候选项时，软件按顺序逐个自动调用回退字体集中的字体，直到候选项字体正常显示为止。因此，如果不能保证主字体是否正常显示候选项，则尽可能多的在回退字体集中设置不同的字体，以供回退之用。
3. 《候选窗口绘制模式》，请尽量选择**<font style="color:#DF2A3F;">2</font>**，以启用回退功能。如果设置为2，但个别字体无法正常显示，请注意回退字体中的字体是否支持显示，如果能支持显示而没有显示，请到QQ群向群主反映。
4. 其它选项，请根据个人爱好自行设置尝试，在此不再逐一介绍。

另外，请注意，小科狗输入法平台支持彩色Emoji的显示（下图），如果需要显示Emoji符号，请自己制作相关码表，或到QQ群下载。

![1682306427667-005ff0c9-4f39-4a77-8bfc-8af2f0708f57.png](./img/DaV-vhgy2BzJ45hC/1682306427667-005ff0c9-4f39-4a77-8bfc-8af2f0708f57-404491.png)![1682306442741-3f655cb6-185e-4e7c-aa18-b0536c44f8e1.png](./img/DaV-vhgy2BzJ45hC/1682306442741-3f655cb6-185e-4e7c-aa18-b0536c44f8e1-598835.png)

### 2.2.4 中英文切换
小科狗可以通过Ctrl、Shift和ctrl+空格键进行中英文的切换，具体请参见方案配置界面的“中英切换配置”项：

![1683115276268-de297cee-ee98-4f56-9dce-c179ff8a2782.png](./img/DaV-vhgy2BzJ45hC/1683115276268-de297cee-ee98-4f56-9dce-c179ff8a2782-850832.png)

具体设置不再赘述，请自行设置体验。

## 2.3 码表的导入、导入及删除
### 2.3.1 码表的导入
#### 1）码表的直接导入
小科狗支持txt格式码表的直接导入，请先新建txt文档，第一行录入码表头文件，如下图（<font style="color:#E8323C;">注意，txt格式注意为带有BOM的UTF-8或者UTF-16，否则会乱码，表头顺序可随意</font>）。

![1658561019446-dbd8d816-a534-47ca-959f-41a893f359ed.png](./img/DaV-vhgy2BzJ45hC/1658561019446-dbd8d816-a534-47ca-959f-41a893f359ed-585145.png)

呼出候选框，鼠标在候选框上面右击选择加载txt码表到内存数据（如下图上），打开码表加载界面，点击“设置码表路径”按钮，加载上面设置好的txt码（如下图下），点击下方的应用或确定，即可将码表导入进码表数据库中。

![1687181887968-ef2f94bf-5ef0-46e1-9e3c-b2f98229e038.png](./img/DaV-vhgy2BzJ45hC/1687181887968-ef2f94bf-5ef0-46e1-9e3c-b2f98229e038-596698.png)

![1658561270028-a530feb2-546e-4684-a9ee-e4de5f5cb559.png](./img/DaV-vhgy2BzJ45hC/1658561270028-a530feb2-546e-4684-a9ee-e4de5f5cb559-136069.png)

将码表导入到码表数据库后，呼出候选框，选择“保存内存数据库到硬盘”，将导入的码表保存到数据库。此时，通过切换码表功能切换到导入的码表进行输入即可（<font style="color:#E8323C;">导入的码表名称即为导入前txt的名称</font>）。

![1687181868210-6d7db465-5faf-4e65-a57c-1dfa70637b52.png](./img/DaV-vhgy2BzJ45hC/1687181868210-6d7db465-5faf-4e65-a57c-1dfa70637b52-440985.png)    ![1658561771103-2e57e34d-ac95-4eac-9697-b22d6aeb6b88.png](./img/DaV-vhgy2BzJ45hC/1658561771103-2e57e34d-ac95-4eac-9697-b22d6aeb6b88-133298.png)

#### 2）通过数据库软件导入
小科狗自带超过20种输入法码表，利用DB Browser for SQLite数据库管理软件（该数据库管理软件为开源软件，推荐使用，也可以根据个人习惯选择其它相关软件）打开小科狗安装文件夹下的keg.db码表数据库文件（下图）。

![1653659102377-6d12275d-1b03-4ef2-9369-8ea527cd06f8.png](./img/DaV-vhgy2BzJ45hC/1653659102377-6d12275d-1b03-4ef2-9369-8ea527cd06f8-147495.png)

小科狗输入法平台可以导入任何输入法码表，在制作码表时，可以依据个人习惯利用文本编辑器、Excel、数据库等软件制作，但是必须保证码表有四个字段：key（编码列）、value（词条列）、weight（权重列）和fc（反查列）字段（下图），否则输入法平台无法识别码表格式，进而导致出现无法打字现象。如果自带的码表中没有自己使用的码表，或者是想导入其它输入法码表，请保证码表中有上述四个字段属性（但是第一行的四个字段值不是必须的），如果没有，则要在DB Browser for SQLite数据库软件中进行添加、修改等操作。

![1653659112894-69769b29-35b2-4a5b-83be-54c91706e075.png](./img/DaV-vhgy2BzJ45hC/1653659112894-69769b29-35b2-4a5b-83be-54c91706e075-044572.png)

选择DB Browser for SQLite数据库软件中的文件—导入—从CSV文件导入表（下图），选择制作好的码表，导入后，选择写入更改，保存码表到数据库中。

![1653659122495-ac5861e1-6103-411a-b2a0-445d1d29e3c6.png](./img/DaV-vhgy2BzJ45hC/1653659122495-ac5861e1-6103-411a-b2a0-445d1d29e3c6-427855.png)

此时的码表字段还没有key（编码列）、value（词条列）、weight（权重列）和fc（反查列）字段，在导入的码表上右击，选择修改表，进入编辑表定义界面（下图）。

![1653659131822-a37c0482-7f2a-4268-94f8-fb01ac50fd29.png](./img/DaV-vhgy2BzJ45hC/1653659131822-a37c0482-7f2a-4268-94f8-fb01ac50fd29-387533.png)

![1653659136503-db4bedc5-ca49-4fee-85af-e6a5f694f264.png](./img/DaV-vhgy2BzJ45hC/1653659136503-db4bedc5-ca49-4fee-85af-e6a5f694f264-906050.png)

根据码表的属性情况，对每个字段进行修改（下图）：

![1653659146674-d0539852-0c59-4b53-9b22-ac7bceb2b81d.png](./img/DaV-vhgy2BzJ45hC/1653659146674-d0539852-0c59-4b53-9b22-ac7bceb2b81d-130256.png)

如果该码表没有反查字段，则要选择增加按钮，添加反查字段（下图）：

![1653659155129-267e4784-1b04-425f-9180-5f433ee69a4e.png](./img/DaV-vhgy2BzJ45hC/1653659155129-267e4784-1b04-425f-9180-5f433ee69a4e-598165.png)

回到小科狗输入法，呼出候选窗口界面，选择“保存内存数据库自硬盘”（下图上），当弹出“更新成功”对话框时，则添加新码表成功，此时再按码表切换方案快捷键，方案列表里面会出现新添加的码表（下图下），选择之后回车即切换到该码表。此后再进入设置编码方案界面对该码表各项参数进行具体设置。

![1653659165395-bf46f354-c7b5-4935-937e-f83298715923.png](./img/DaV-vhgy2BzJ45hC/1653659165395-bf46f354-c7b5-4935-937e-f83298715923-426522.png)

![1653659173050-beb49e28-d846-463a-bfc8-1ef641ac8fa1.png](./img/DaV-vhgy2BzJ45hC/1653659173050-beb49e28-d846-463a-bfc8-1ef641ac8fa1-426584.png)

### 2.3.2 码表的导出
小科狗输入法支持码表的直接导出，在候选框或者状态条上右击呼出菜单栏，选择“导出txt码表到指定文件夹”，呼出码表导出对话框。

![1687182131876-8cfae4dc-9251-443c-a1e7-79b4aacef064.png](./img/DaV-vhgy2BzJ45hC/1687182131876-8cfae4dc-9251-443c-a1e7-79b4aacef064-672447.png)

![1687182199123-64c8a334-a159-409d-866b-c38fded514e1.png](./img/DaV-vhgy2BzJ45hC/1687182199123-64c8a334-a159-409d-866b-c38fded514e1-690591.png)

码表导出界面由两部分组成，上面为码表导出文件夹选择（目标文件夹），默认为空，下面为设置所要导出的码表（目标码表名）。

点击右侧的“选择文件夹”按钮，选择需要导出到的文件夹位置即可。

“目标码表名”中默认显示的为当前所用码表名称（例如上图中当前所用码表为86五笔），也可以在此对话框中进行自定义需要导出的码表名，可能是一个，也可以是多个，如果需要一次需要导出多个码表时，码表名中间需要用#链接，如果需要将所有码表导出，则不需要此对话框中一一列出，直接勾选上面的“导出全部码表”即可。

**<font style="color:#DF2A3F;">注意</font>**，“目标码表名”对话框中所输入的码表名必须和码表数据库中的码表名称保持一致，否则可能出错。

### 2.3.3 码表的删除
#### 1）码表的直接删除
按快捷键呼出码表菜单（详见说明开关的快捷键一览表节），利用上下箭头移到到需要删除的码表上面，按Ctrl+DEL快捷键即可将选中的码表删除。

#### 2）通过数据库软件删除
如果不想要数据库中的相关码表，想将其删除，则在DB Browser for SQLite软件中，选中对应码表，右击删除表即可（下图），然后在小科狗中呼出候选框界面，再次“<font style="color:red;">保存内存数据库到硬盘</font>”即可。

![1653659182952-fe50488e-3598-4d8a-b577-523ce43d2304.png](./img/DaV-vhgy2BzJ45hC/1653659182952-fe50488e-3598-4d8a-b577-523ce43d2304-496330.png)

### 2.3.3 大词库功能
小科狗支持百万级大词库功能，实现方式为将码表命名为以“大词库”开头即可。例如原有码表名为“86五笔”，要想实现百万级大词库支持功能，将码表命名为“大词库86五笔”即可。

**<font style="color:#DF2A3F;">注意</font>**：百万级大词库功能会影响到调频等功能，望周知。

## 2.4 反查功能
### 2.4.1 利用小科狗码表进行反查
小科狗可以进行字词反查，例如在使用形码输入法时，不知道某些字的拆分方法，可以利用拼音快速反查进行拆分学习（下图上），再例如不知道某些字词的读音，可以利用反查快速获取（下图下）。

![1653659193890-ddb1bd5d-f3c4-4be7-bac0-783ec65549bd.png](./img/DaV-vhgy2BzJ45hC/1653659193890-ddb1bd5d-f3c4-4be7-bac0-783ec65549bd-763449.png)

![1653659197938-fc4f840c-046a-4689-988d-3ec2da29f2f2.png](./img/DaV-vhgy2BzJ45hC/1653659197938-fc4f840c-046a-4689-988d-3ec2da29f2f2-653441.png)

想要反查功能的实现，首先需要制作反查码表（下图），本说明以86五笔反查为例，对反查码表制作进行说明。

![1653659206063-4630691c-78a3-4726-9c92-9684c8b9e3e9.png](./img/DaV-vhgy2BzJ45hC/1653659206063-4630691c-78a3-4726-9c92-9684c8b9e3e9-170350.png)

如下图所示，为86五笔六千多字单字反查拆分码表（可在官方QQ交流群下载），其中第一列为反查列（fc），第二列为编码列（key），第三列为词条列（value），第四列为权重列（weight），各列的顺序可以任意，并非要固定不变。将其导入到DB Browser for SQLite软件中，对各列的字段属性进行重新命名，再呼出小科狗候选界面，更新内存数据库，切换到反查码表，就可以进行拆分输入了。但是实际使用中并不会进行单独的拆分输入，而是把它当成临时码表而临时使用，至于利用引导键引导临时码表功能，请看 **<font style="color:#E8323C;">3.5 引导码表设置</font>**说明。

![1653659215767-fe4eca5b-5b54-476f-a2ff-b03ff3329ad0.png](./img/DaV-vhgy2BzJ45hC/1653659215767-fe4eca5b-5b54-476f-a2ff-b03ff3329ad0-767429.png)

#### 2.4.1.1 反查的开启与关闭
小科狗助手可以利用快捷键（ctrl+j）开启或关闭反查内容<font style="color:#DF2A3F;">（注意，请在候选框显示的情况下按快捷键，否则无效）</font>。当呼出候选框后，按ctrl+j，如果编码串位置提示“反查显示已关闭”（下图左），则候选项不再显示反查内容，反之则显示反查内（下图右）。

![1662377226084-9414e09b-aa7c-43e9-abd6-401a69d40a9f.png](./img/DaV-vhgy2BzJ45hC/1662377226084-9414e09b-aa7c-43e9-abd6-401a69d40a9f-126443.png)![1662377254826-733de01f-43bf-4989-b51d-2baaa0c8dfc0.png](./img/DaV-vhgy2BzJ45hC/1662377254826-733de01f-43bf-4989-b51d-2baaa0c8dfc0-313121.png)

### 2.4.2 利用小科狗配置助手进行反查
小科狗配置助手具有划选反查功能。具体设置为在“码表”设置项下勾选“划选反查”，此时，如果输入字词上屏，用鼠标选中该字词，按鼠标中键（滚轮），即弹出反查界面，如下图所示：

![1653659235008-87bd7fd5-3b79-4e53-8707-c93f5aa4d9e2.png](./img/DaV-vhgy2BzJ45hC/1653659235008-87bd7fd5-3b79-4e53-8707-c93f5aa4d9e2-253134.png)

注意，想要在反查界面显示码元拆分结果和单字读音结果，需要当前码表反查字段有反查属性，反查内容的添加，需要手动添加或者是86、98五笔码表时直接使用配置助手添加，具体操作见2）通过小科狗配件助手进行加词：<font style="color:black;">。另外，当前码表的字体必须是拆分码表对应的字体，否则无法正常显示拆分结果（86和98五笔使用的字体为98WB-1，请到QQ群下载、安装）。上图中的单字读音显示的同样为拆分结果，是因为反查字段中并无读音，仅有拆分。</font>

<font style="color:red;">当划选了词后，会在反查界面显示词里面的每个单字反查结果，请用左右箭头选择需要显示的单字。如果给当前码表添加了反查内容，而划选结果中没有反查内容显示，请重启小科狗配置助手。</font>

## 2.5 引导码表设置
小科狗的特色之一，即为可以利用三个引导键引导多达6个任意码表，其设置在码表配置界面最下面的“引导码表检索设置”项(下图)。

![1653659244791-103baf89-96ed-4611-98dd-638fdb5b79f8.png](./img/DaV-vhgy2BzJ45hC/1653659244791-103baf89-96ed-4611-98dd-638fdb5b79f8-013607.png)

引导快捷键必须是英文状态小写符号。默认第一个引导键为`键（Tab上方按键），第二个引导键为数字1，第三个引导键为2。如果要对各引导键进行更新，直接替换默认按键即可，并且各引导键所引导的码表必须是当前码表数据库已有码表，否则引导无效。当在输入时按一下引导键引导第一个码表，连按两个引导键则引导第二个码表。

以86五笔为例，在平时输入时主码表为86五笔，而第一个引导键为分号键，引导了两个码表：英文宝典和拼音反查。也就是说在利用86五笔输入时，按一下分号键，进入临时码表：英文宝典，此时输入时，直接输入编码串（key）（下图上），出现的即为词条列（value），如下图下所示：

![1653659276490-83502023-ed40-4c23-9afa-673cc302b5d1.png](./img/DaV-vhgy2BzJ45hC/1653659276490-83502023-ed40-4c23-9afa-673cc302b5d1-234354.png)

![1653659289668-e0a8cc31-4eb3-4075-9c91-48cdd50e8128.png](./img/DaV-vhgy2BzJ45hC/1653659289668-e0a8cc31-4eb3-4075-9c91-48cdd50e8128-895225.png)

当按两个分号按时，进入临时码表拼音反查（下图）：

![1653659298261-a4d64e09-9f72-42db-b2df-8e113e015375.png](./img/DaV-vhgy2BzJ45hC/1653659298261-a4d64e09-9f72-42db-b2df-8e113e015375-315355.png)![1653659300914-04d71b4e-0785-4dd4-a922-b8ccc4040f15.png](./img/DaV-vhgy2BzJ45hC/1653659300914-04d71b4e-0785-4dd4-a922-b8ccc4040f15-995786.png)

第二个引导键为z键，一个z时，引导了86生僻字（下图上）；两个z键时引导了码表符号（下图中）：

![1653659311948-cd49b369-158f-4780-bfeb-fd56c5980c12.png](./img/DaV-vhgy2BzJ45hC/1653659311948-cd49b369-158f-4780-bfeb-fd56c5980c12-121486.png)![1653659319879-9a045acb-9a4f-477d-99a7-d82e4f903616.png](./img/DaV-vhgy2BzJ45hC/1653659319879-9a045acb-9a4f-477d-99a7-d82e4f903616-518044.png)



![1653659325460-d4e9e707-5944-4dbe-8090-a76f4e4f726f.png](./img/DaV-vhgy2BzJ45hC/1653659325460-d4e9e707-5944-4dbe-8090-a76f4e4f726f-627847.png)![1653659328401-57faedc8-f723-4b32-b82a-87846d8cd849.png](./img/DaV-vhgy2BzJ45hC/1653659328401-57faedc8-f723-4b32-b82a-87846d8cd849-424701.png)

至于码表后的<1={[s]}>和<1=z>，请看**<font style="color:#E8323C;">3.17 过渡态的设置</font>**。

![1660123702564-8872991c-3683-4ce9-a447-db32cf512611.gif](./img/DaV-vhgy2BzJ45hC/1660123702564-8872991c-3683-4ce9-a447-db32cf512611-193425.gif)

## 2.6 主辅码表切换
小科狗另外一个特色功能是可以利用快捷键进行主辅码表切换：当给主码表设置了辅码表后，如果敲击快捷键，即可直接切换到辅码表，再次敲击快捷键，回到主码表。此功能在码表配置界面中的“临时码表检索”项设置。

![1653659396165-e83ac931-d362-496e-84f8-b91add1116dc.png](./img/DaV-vhgy2BzJ45hC/1653659396165-e83ac931-d362-496e-84f8-b91add1116dc-894135.png)

例如当前主码表是86五笔，给其设置的辅码表为英文宝典（上图），引导快捷键为`（Tab上面的按键）（**<font style="color:#DF2A3F;">当不需要引导辅码表时，删除引导快捷键即可</font>**）。主码表没有设置辅码表时，候选框界面编码串前面没有数字出现（下图左），如果设置了辅码表，则编码串前面有1：出现，表示当前为主码表状态（下图右）。

![1653659402162-f04c0797-6090-4a40-85d2-ed55ed833777.png](./img/DaV-vhgy2BzJ45hC/1653659402162-f04c0797-6090-4a40-85d2-ed55ed833777-657674.png)![1653659404614-a56d3ba0-b117-4b25-b58c-d45a115ff5b6.png](./img/DaV-vhgy2BzJ45hC/1653659404614-a56d3ba0-b117-4b25-b58c-d45a115ff5b6-202756.png)

在给主码表设置有辅码表的情况下，按引导快捷键，进行辅码表输入状态（下图左），此时输入时，不再是主码表，而是辅码表了，并且编码串前面有2：出现，表示当前为辅码表状态（下图右）。

![1653659411930-808150a4-4306-4fd2-be44-f108d1b655d4.png](./img/DaV-vhgy2BzJ45hC/1653659411930-808150a4-4306-4fd2-be44-f108d1b655d4-961519.png)![1653659414434-ee9c4b90-c62e-4ba6-8e1e-3c2be50b4217.png](./img/DaV-vhgy2BzJ45hC/1653659414434-ee9c4b90-c62e-4ba6-8e1e-3c2be50b4217-084965.png)

## 2.7 小科狗和小狼毫、大科狗之间的切换
小科狗输入法平台可以利用快捷键方便快捷的切换到小狼毫模式或者大科狗模式，具体请打开安装包里面的“<font style="color:rgb(38, 38, 38);">keg.txt</font>”查看（下图）。

![1653659424751-c173b28e-a022-428f-afb2-87612b93f869.png](./img/DaV-vhgy2BzJ45hC/1653659424751-c173b28e-a022-428f-afb2-87612b93f869-016067.png)

当在小科狗模式下时，按ctrl+F10，即切换到小狼毫模式，此时再输入时，即直接调用小狼毫输入法，而不再是小科狗输入法。但是注意，小狼毫输入法要起作用，需要小狼毫服务器处于运行状态，使得小科狗能够直接调小狼毫。有两种方法可以实现：1）在“<font style="color:rgb(38, 38, 38);">keg.txt</font>”中，将最正下方的“C:\weasel-0.14.3\WeaselServer.exe”，改为小狼毫绿色非安装版程序（该程序请在官方QQ交流群下载）所在目录，例如：E:\软件\输入法\小狼毫\小狼毫绿色版\weasel-0.14.3\WeaselServer.exe，此时按ctrl+F10，小狼毫服务器即自行启动，能够被小科狗调用。2）可自行到小狼毫输入法官网下载最新版小狼毫输入法安装包进行安装，如安装成功，则按ctrl+F10时，也同样会切换到小狼毫输入法状态。至于切换到小狼毫输入法之后小狼毫的各种配置方法，请自行进行网络搜索。

当按下ctrl+F9时，即可切换到大科狗输入法模式，可以到官方QQ交流群（641858743）下载大科狗使用（NewKegInput.zip），大科狗功能较小科狗更加丰富。至于大科狗的具体设置，请查看安装包下的大科狗使用说明。

<font style="color:#DF2A3F;">若不喜欢F7、F8、F9和F10设置方式，请安装小科狗马上删除C:\Windows\TSF\SiKegInput\KegKeySet.txt文件中的对应快捷键。</font>

## 2.8 增词和删词功能
### 2.8.1 添加词条功能
小科狗目前可以采用三种方式进行增加字词的操作：

#### 1）通过数据库软件加词
当你想要增加词语时，请用DB Browser for SQLite打开Keg.db码表数据库，浏览你所要增加词语的码表，点击在当前表中插入一条新纪录，随后在对应字段里面录入对应属性的字段即可，录入后点击右下角的应用，再点击左上的写入更改，最后呼出候选窗口右击选择“保存内存数据库到硬盘”即可生效（下图）。

![1653659436279-868218a0-de30-4d32-9f99-4a5a20725d36.png](./img/DaV-vhgy2BzJ45hC/1653659436279-868218a0-de30-4d32-9f99-4a5a20725d36-210423.png)

#### 2）通过小科狗配件助手进行加词
请在QQ交流群文件—辅助工具文件夹中下载“小科狗配置助手工具”，打开小科狗配置助手，选择“码表”项，点击“批量添加词条”或者按设置的加词热键，启动快捷批量加词界面（下图下），根据提示，在窗口输入想要添加的词，关闭窗口，即可将该词条自动添加到对应码表。

配置助手内置了86五笔和98五笔拆分码表，如果选择对应拆分码表，并勾选后面的对话框，则添加词条时，会在码表的反查字段中自动添加拆分结果。如果想给当前所选方案码表整个码表添加拆分反查结果，请点击“插入反查列”按钮即可，如果不想要拆分反查结果，则点击“清空反查列”按钮。在插入拆分结果后，如要在候选框显示拆分结果，则要进行设置，请看**<font style="color:#E8323C;">3.2.2之6）中《要显示反查提示吗?=要》</font>**设置项。

![1653659446859-d8e540b0-9556-48bd-b43c-28d1804fef4e.png](./img/DaV-vhgy2BzJ45hC/1653659446859-d8e540b0-9556-48bd-b43c-28d1804fef4e-228743.png)

![1653659457043-4781986d-4240-4206-9503-bfda1b384ad6.png](./img/DaV-vhgy2BzJ45hC/1653659457043-4781986d-4240-4206-9503-bfda1b384ad6-552821.png)

注意，该加词功能具有过滤功能，会过滤掉码表已有词条，在加词时，请先手动删除快捷批量加词对话框里面的文字，再添加词条；其已经内置了王码方案，因此王码码表添加词条时，只需要直接输入添加的词条即可，而不需要输入词条对应的编码串，王码以外的码表添加词条时，清严格按照“编码=词条”的格式录入（上图）。

#### 3）通过小科狗内置加词功能加词
小科狗内置了界面化加词功能，默认打开加词对话框的快捷键为Ctrl+Ins，请按快捷键打开小科狗输入法手动加词对话框，该对话框分上方的单词条添加区和下文的批量加词区（下图）。

![1653659465486-921f7e7f-b039-4f8b-9afd-f8e46a26cb62.png](./img/DaV-vhgy2BzJ45hC/1653659465486-921f7e7f-b039-4f8b-9afd-f8e46a26cb62-076735.png)

该功能支持单词条添加和批量加词。如果想添加单一词条，请在key后面输入添加词条的编码串，在value后面输入对应的词条，weight为词条权重，默认为0，也可手动输入权重值，fc为反查内容，可以是任何内容，只是一般反查列放入拆分结果、读音等，请自行输移需要显示的反查内容。此时点击确定、应用、直接应用或按回车键，均可将该词条加入到对应码表中；如果想批量加词，在上方输入三个属性值后，可以点击“增加”按钮，将该词条移到下方批量加词对话框，重复该操作，直到将要添加的所有词条显示在下方批量加词区，此时点击确定、应用、直接应用或按回车键即将所有词条添加到对应码表（下图），最后呼出候选窗口右击选择“保存内存数据库到硬盘”即可生效。

![1653659474328-0bff9e40-9444-49fd-bb75-06c8a25d4c0b.png](./img/DaV-vhgy2BzJ45hC/1653659474328-0bff9e40-9444-49fd-bb75-06c8a25d4c0b-950598.png)

#### 4）自动造词功能
小科狗可以进行半自动化的自动造词。具体参见配置界面的“自动造词设置”项：

![1683293134416-04422702-854d-40b9-a00d-dc0a385713bf.png](./img/DaV-vhgy2BzJ45hC/1683293134416-04422702-854d-40b9-a00d-dc0a385713bf-968925.png)

如果想开启自动造词功能，则将不要改为要。此时，在中文状态下打出编码时，如码表中无该词，按返回键，然后把空编码对应的汉字上屏，再按返回键，此时编码与刚才上屏的汉字自动组合成词并存储于码表当中。

### 2.8.2 删除词条及码表功能
#### 1）通过数据库软件删除词条
如果想要删除冗余的字词，可以同样利用DB Browser for SQLite打开Keg.db码表数据库，浏览到对应码表，选择对应字词，删除之，再点击左上的写入更改（下图），最后呼出候选窗口右击选择“保存内存数据库到硬盘”即可生效。

![1653659496873-b8eb006e-617d-4e67-ade5-01286a03d28e.png](./img/DaV-vhgy2BzJ45hC/1653659496873-b8eb006e-617d-4e67-ade5-01286a03d28e-705479.png)

#### 2）通过快捷键删除词条
此外小科狗支持快捷键删词功能：在输入的过程当中，如果想删除某一字词，利用上下箭头移动候选光标到想删除的候选项上，同时按下Ctrl+Del键，即可将选中的候选项删除掉。<font style="color:red;">注意，删词操作无法撤消，请慎用。如果误删字词，请用上面的增加字词方法重新添加到对应码表即可</font>。

#### 3）通过快捷键删除码表
按码表选择方案快捷键（**<font style="color:#E8323C;">参见1.2节</font>**），呼出方案列表，选中需要删除的码表方案，按Ctrl+Del，即可删除选中的码表方案。

## 2.9 字词顺序调整
小科狗对于候选框字词顺序的调整，可通过对“码表调频与检索”下的设置实现以下功能：

> 1）**<font style="color:#2F54EB;">固定顺序</font>**，即顺序不变，即所有字词的顺序固定不变，要实现些功能，请设置《要启用上屏自动增加调频权重吗?=不要》，设置《要启用上屏自动增加调频权重直接到顶吗?=不要》，设置《候选词条要按码长短优先排序吗?=不要》，设置《候选词条要按调频权重捡索排序吗?=不要》。
>
> 2）**<font style="color:#2F54EB;">一次到顶功能</font>**，即为输入某个字词时，如果不在第一候选，通过选重上屏后，该候选项即可直接变为第一候选。要实现此功能，设置《要启用上屏自动增加调频权重吗?=要》，设置《要启用上屏自动增加调频权重直接到顶吗?=要》，设置《候选词条要按码长短优先排序吗?=要》，设置《候选词条要按调频权重捡索排序吗?=要》。
>
> 3）**<font style="color:#2F54EB;">渐次到顶功能</font>**，即为输入某个字词时，如果不在第一候选，则每输入一次该字词会前移一位（即权重值加1），直至变为第一个候选项。要实现此功能，请设置《要启用上屏自动增加调频权重吗?=要》，设置《要启用上屏自动增加调频权重直接到顶吗?=不要》，设置《候选词条要按码长短优先排序吗?=要》，设置《候选词条要按调频权重捡索排序吗?=要》
>
> 4）**<font style="color:#2F54EB;">手动调整顺序</font>**，即通过快捷键手动调整某个候选项到第一候选项，要实现该功能，请在候选项界面按Ctrl+对应候选项前面序号即可。**<font style="color:red;">注意：要看到手动调频效果，请将《候选词条要按调频权重捡索排序吗?=要》改为要即可。</font>**
>

## 2.10 顶功功能
小科狗可以自定义顶功功能，具体设置见设置面板里的“顶功”，主要包括设置顶功所用的小集码元：《顶功小集码元》，以及顶功规则的选择。实际上顶功功能不仅指此处的利用大小集码元来顶功，如**<font style="color:#E8323C;">3.2.2 </font>****<font style="color:#E8323C;">重复上屏、码表调频与检索设置</font>**里面的12)—15)项之设置，其中的码长顶屏、唯一顶屏、唯一上屏和标点顶屏均属于顶功功能，具体可以尝试不同的设置，自行体验。

但是不同的顶功功能又有所区别，具体说来可以大概分为以下三种：1）选择上屏，包括空格上屏，回车上屏和选重上屏。其中形码中空格上屏用的最多，而回车上屏较少，选重上屏如数字（拼音方案使用较多）、标点和字母等选重上屏等。2）唯一上屏，即候选顶唯一时直接上屏。3）顶功上屏，包括限长顶屏、非码顶屏和空码顶屏等[<sup>[1]</sup>](#_ftn1)。以上顶功功能详情本说明不再赘述，有意者可自行搜索学习。

本说明仅对顶功上屏做简单说明：顶功上屏，简称顶屏，是指通过后续的输入将前一字词顶上屏幕，从而省去选择键，通常是空格键。顶屏的前提是被顶的字词已经完全确定。在有多个选项的时候，被顶的往往是第一个选项，当然理论上也可以将其它某一选项作为被顶的对象。顶功上屏和选择上屏的区别是，前者的顶屏键原来的作用不会发生变化，而后者的选择键失去了其原来的作用。比如，如果用「;」顶屏，那么在顶出字词的同时也输入了「;」，而在用「;」来选重的时候，在上屏所选的字词后，「;」本身就被“吃掉”了，就消失了<sup></sup>[<sup>[1]</sup>](#_ftn1)。

小科狗中的顶功即属于顶功上屏，其本质是为了省略按空格上屏这一步骤。要使用该功能，需要先在《顶功小集码元》中设置小集码元，其设置方法为直接输入小写形式的键盘键即可。至于设置哪些字母、标点符号等为小集码元，因其牵扯到码表、打字规则、使用习惯等因素，设置比较复杂且想要习惯可能需要较长时间，请使用者自行尝试。在设置好顶功小集码元后，在顶功规则中选择3个规则中的某一规则自行体验其不同之处。

## 2.11 外挂小软件的使用
在安装文件下面的tools工具当中，内置了QQ拼音截图等小插件，也可以自己在网上下载其它小软件放到该文件夹内。

“<font style="color:rgb(38, 38, 38);">keg.txt</font>”文本文件里面的“当前文件夹的tools\QInputV2.exe”即指外挂小软件AHK启动程序。该启动程序基于AHK V2版本编写，如对AHK了解，可自行添加其它自己需要的内容。**<font style="color:red;">如果不想让外挂小软件随输入法启动，则在“使用说明”里面将QInputV2.exe前面的路径改为无效路径或者直接删除该行即可。</font>**

用记事本打开tools文件夹下面的QInputV2.ahk，AHK代码内容如下图所示，其中箭头所示处为快捷按键，例如^q表示为快捷按键ctrl+q，^a表示快捷按键ctrl+a，以此类推；红色椭圆圈处的为快捷按键要启动的软件，将要启动的软件放入tools文件夹中，并将其名称（连同后缀名）一同复制粘贴到该处即可。重启小科狗服务器，试着按相应的快捷键，启动对应的软件进行相应的操作。



当外挂小插件启动后，任务栏会出现如下图上AHK图标，表明外挂小插件启动成功，如果不想在任务栏显示该图标，请手动打开安装文件夹下面的tools文件夹，利用记事本打开QInputV2.ahk，将里面;#NoTrayIcon一行前面的分号删除（下图下），保存更改，重启小科狗服务，则外挂小插件的图标即被隐藏。

![1653659674915-c0f99d29-e7de-4005-9558-4625307de19a.png](./img/DaV-vhgy2BzJ45hC/1653659674915-c0f99d29-e7de-4005-9558-4625307de19a-944340.png)

![1653659678514-49bda27b-2589-4f58-b68d-94706e35add0.png](./img/DaV-vhgy2BzJ45hC/1653659678514-49bda27b-2589-4f58-b68d-94706e35add0-706126.png)

## 2.12 日期的快速输出
小科狗输入法平台可通过自定义来快速灵活的输入不同形式的日期、时间等（下图）。

![1653659690011-00087902-5028-4971-ab23-d8ef3373b415.png](./img/DaV-vhgy2BzJ45hC/1653659690011-00087902-5028-4971-ab23-d8ef3373b415-438177.png)

![1653659693380-3913d945-4196-43bd-a036-e73bd289a891.png](./img/DaV-vhgy2BzJ45hC/1653659693380-3913d945-4196-43bd-a036-e73bd289a891-481198.png)

如果要实现日期等的快速输入，则需要根据配置规则将相应的代码放到对应的码表中去。小科狗输入法平台可以将日期输入规则直接放到常用的主码表里，也可以单独新建独立日期码表，将日期码表当成临时码表或者辅码表，利用快捷键来调用，此种方法见**<font style="color:red;">3</font>****<font style="color:#E8323C;">.5 引导码表设置</font>**和**<font style="color:#E8323C;">3.6 主辅码表切换</font>**小节的说明。以下仅对将日期输入规则直接放到主码表情况进行说明，而将日期作为独立码表方法相同。

日期输入规则：

> <font style="color:red;">时间结果前加t，格式参考MFC的CTime类的格式码为：</font>
>
> <font style="color:red;">%a：周的英文缩写形式。%A：周的英文全名形式。</font>
>
> <font style="color:red;">%b：月的英文缩写形式。%B：月的英文全名形式。</font>
>
> <font style="color:red;">%c：完整的日期和时间。</font>
>
> <font style="color:red;">%d：十进制形式的日期（01-31）。</font>
>
> <font style="color:red;">%H：24小时制的小时（00-23）。%I： 12小时制的小时（00-11）。</font>
>
> <font style="color:red;">%j：十进制表示的一年中的第几天（001-366）。</font>
>
> <font style="color:red;">%m：月的十进制表示（01-12）。</font>
>
> <font style="color:red;">%M：十进制表示的分钟（00-59）。</font>
>
> <font style="color:red;">%p： 12小时制的上下午标示（AM/PM）。</font>
>
> <font style="color:red;">%S：十进制表示的秒（00-59）。</font>
>
> <font style="color:red;">%U：一年中的第几个星期（00-51），星期日是一周的第一天。</font>
>
> <font style="color:red;">%W：一年中的第几个星期（00-51），星期一是一周的第一天。%w：十进制表示的星期几（0-6）。</font>
>
> <font style="color:red;">%Y：十进制表示的年。前加#去掉0}</font>
>

用QQ群下载的DB Browser for SQlite数据库软件打开词库码表（下图上），或按Ctrl+ins打开小科狗手动加词界面（下图下），利用上文所示规则，将需要表示的日期规则放到码表中的value字段，将日期规则对应的编码放入到key字段，并点击写入更改，在任意软件里面呼出候选框，在候选框上右击，选择更新内存数据库，待更新成功后，输入日期对应的编码，即可成功输出编码对应的日期了。

![1653659704623-3e2c78de-70d7-451e-a761-40c774bf5d29.png](./img/DaV-vhgy2BzJ45hC/1653659704623-3e2c78de-70d7-451e-a761-40c774bf5d29-672637.png)

![1683337487176-d320c9b6-0668-45de-ac1a-dc10cb9762c5.png](./img/DaV-vhgy2BzJ45hC/1683337487176-d320c9b6-0668-45de-ac1a-dc10cb9762c5-422700.png)

部分日期示例规则及其对应输出日期形式如下所示，具体在码表中的录入形式如上图：

> ①  规则：date: t%Y%m%d        
>
> 通过输入date编码输出日期格式：20211212
>
> ②  规则：date: t%Y/%m/%d
>
> 通过输入date编码输出日期格式：2021/12/12
>
> ③  规则：date: t%Y年%#m月%#d日
>
> 通过输入date编码输出日期格式：2021年12月12日
>
> ④  规则：date: t%cY年%cm月%cd日
>
> 过输入date编码输出日期格式：二〇二一年十二月十二日
>
> ⑤  规则：date: t%cyY年%cym月%cyd
>
> 过输入date编码输出农历日期格式：二〇二一年十一月初九
>
> ⑥  规则：week: t星期%cw
>
> 过输入date编码输出日期格式：星期日
>
> ⑦   规则：time: t %H:%M:%S
>
> 过输入time编码输出日期格式：22:59:45
>

当然，以上仅为示例，可以根据红字所示规则，将任意年、月、日、星期、时、分、秒、阳历和阴历等进行组合，并且可以利用不同的连字符将各个日期之间进行连接，形成不同的日期格式等等，不再赘述，请自行尝试。

## 2.13 截图功能
由于抽风，开发者开发了一个小软件，配合小科狗和小狼毫等输入法平台使用，可以达到截图上屏刷屏的功能，但是要提醒广大使用者，要慎用此功能，避免由于刷屏过多被踢群或者挨骂。

### 2.13.1 小科狗内置截图功能
#### 1）候选框截图上屏
小科狗内置了整个候选框截图上屏功能，如果想要使用此功能，需要将要截图的文字敲出，但不要上屏（如果状态为四码唯一直接上屏，可以根据上文介绍，取消四码唯一上屏，或者仅敲前三码），此时按Ctrl（左右均可）+Enter，则候选框会整个截图上屏。该说明文档中的候选框截图均用此功能实现。此外，该功能可以将别人电脑无法显示的大字集文字作为截图发送，方便交流。在利用快捷键截图时，可以结合候选框的放大缩小功能使用，清晰显示笔画复杂的文字，效果更佳（如下图 biang biang 面的biang字）。

![1653659717720-c94ad212-e68b-40a2-9749-be209ade9f1b.png](./img/DaV-vhgy2BzJ45hC/1653659717720-c94ad212-e68b-40a2-9749-be209ade9f1b-346292.png)![1653659720239-79a5aca8-91c5-4413-8c69-db5c14e69453.png](./img/DaV-vhgy2BzJ45hC/1653659720239-79a5aca8-91c5-4413-8c69-db5c14e69453-258907.png)

图62

#### 2）单字截图上屏
小科狗内置了单个文字截图功能，和上文所述候选框截图所不同的是，小科狗内置的单字截图功能，仅截图要上屏的无底色候选项文字，其快捷键为Ctrl+shift+}<font style="color:rgb(223, 42, 63);">（注意，请在候选框显示的情况下按快捷键，否则无效）</font>，当按快捷键后，会弹出提示框提示“启用了截图上屏功能”，此时如果输入了字或词，按空格键或者选重键，则直接将第一候选项或选择的候选项文字截图上屏，如果想停用截图功能，则再次按Ctrl+}，会弹出提示框提示“取消了截图上屏功能”。

如下图所示为在word中利用小科狗截图功能帖入图片文字，其中某一个字为文字图片，而非纯文字，你能找出来么？

![1653659729112-c3206992-dd59-4ce6-b789-f8befbc52c74.png](./img/DaV-vhgy2BzJ45hC/1653659729112-c3206992-dd59-4ce6-b789-f8befbc52c74-375080.png)

其中的“哪”字为文字图片。

如果想要实现此功能，请在需要插入文字图片的地方先将需要插入的图片文字插入，在插入文字图片时需要注意字体的统一，例如下图，纯文字用的是宋体小四，则需要在小科狗配置中将当前码表的字体改为宋体，必须保证输入法候选框字体和word文档字体的统一。

![1653659739597-1db722c2-0106-4859-9b33-23e98aa9435e.png](./img/DaV-vhgy2BzJ45hC/1653659739597-1db722c2-0106-4859-9b33-23e98aa9435e-276379.png)

选中段落，点击“段落”右下角的符号，进入段落设置，将“中文版式”下的“文本对齐方式”改为“居中”（下图上），此时文字图片已经和纯文字段落居中对齐，点击文字图片，在“格式”中将图片大小改为0.44厘米（经过试验，文字图片如果要和小四宋体文字保持大小一致，则需要设置图片大小为0.44厘米，其它字体大小对应的文字图片大小，可自行试验），此时的效果如下图下所示。

![1653659749753-21ab8928-d589-4d55-97be-2140a63150dc.png](./img/DaV-vhgy2BzJ45hC/1653659749753-21ab8928-d589-4d55-97be-2140a63150dc-714292.png)

![1653659753953-64068a07-ce06-42dc-85ee-c3aa30a0ec06.png](./img/DaV-vhgy2BzJ45hC/1653659753953-64068a07-ce06-42dc-85ee-c3aa30a0ec06-793507.png)

该功能可用于电脑不支持的字体，或者大字集字体与纯文本的混合排版中，如果文章、古集中无法显示大字集字体，此时用文字图片功能直接插入图片，文字图片能和纯文本完美结合，达到便捷性和实用性的统一。

<font style="color:red;">注意：截图上屏时不能码长顶屏，但是可以唯一顶屏和标点顶屏。</font>

### 2.13.2 外挂截图工具
#### 1）候选框截图工具
![1653659763335-84cba280-fcc3-4ec4-a8e8-bce97c6a7c5f.png](./img/DaV-vhgy2BzJ45hC/1653659763335-84cba280-fcc3-4ec4-a8e8-bce97c6a7c5f-753061.png)

请在官方交流群下载“截候选窗口”工具，解压到电脑任意位置，打开其中的setting.ini进行快捷键的配置。其中QkgHotkey为软件截图功能开关快捷键，可以自定义，如图中所示为^`，其中Ctrl使用^代替，`为tab上面的按键，快捷键具体使用方法可以参考AHK语法。Hotkey为上屏截图快捷键，当在候选窗口打出想要上屏的字后，按该键，即直接截图上屏，一般建议使用空格键，即Space默认设置即可。

![1653659771978-c1ac6736-78e0-4e4d-ad20-7d9d875c240e.png](./img/DaV-vhgy2BzJ45hC/1653659771978-c1ac6736-78e0-4e4d-ad20-7d9d875c240e-752014.png)![1653659774652-3d323f9b-aad3-497d-8bd8-1a3048f13c5c.png](./img/DaV-vhgy2BzJ45hC/1653659774652-3d323f9b-aad3-497d-8bd8-1a3048f13c5c-033484.png)

设置好两个快捷键后，双击启动截候选窗口.exe启动软件，默认截图功能开启，此时利用小科狗或者小狼毫输入完某个字词时，按空格键，则直接上屏该候选窗口，如果想关闭截图上屏功能，则按上文设置的开关快捷键即可关闭截图功能。由于输入法软件差异，该软件在某些输入法软件中可能不起作用。

#### 2）单字截图功能
请在群文件中“辅助工具”文件夹下根据需要下载候选框图工具小插件，其中7z压缩文件为未打包原程序。

![1653659783949-c66caa42-b275-451d-9b2c-08f9c9be9c4b.png](./img/DaV-vhgy2BzJ45hC/1653659783949-c66caa42-b275-451d-9b2c-08f9c9be9c4b-443706.png)

首先设置启动快捷键，设置方法参见**<font style="color:#E8323C;">2.11 外挂小软件的使用</font>**说明项，本说明中将其快捷键设置为Ctrl+Shift+x，当按下该快捷键时，即打开候选截图设置界面，勾选“监控QQ消息逐字发送”功能时，则在QQ、微信等输入文字，回车发送文字后，会自动上屏单字截图，勾选“随机字体色”时（下图1、2图），则截图颜色随机（下图3、4、5图）。

![1653659792927-395bbc17-4322-414c-88f3-5d0085f9d1e4.png](./img/DaV-vhgy2BzJ45hC/1653659792927-395bbc17-4322-414c-88f3-5d0085f9d1e4-641639.png)![1653659795291-5f4dccd1-3e91-4011-96bf-530e57ddd3b4.png](./img/DaV-vhgy2BzJ45hC/1653659795291-5f4dccd1-3e91-4011-96bf-530e57ddd3b4-529562.png)

![1653659817974-72f4df1b-ebca-4a5f-bfbe-c71f46ef191e.png](./img/DaV-vhgy2BzJ45hC/1653659817974-72f4df1b-ebca-4a5f-bfbe-c71f46ef191e-039443.png)

![1653659822343-7b8f30de-80cb-421d-b7c9-28df23819a9e.png](./img/DaV-vhgy2BzJ45hC/1653659822343-7b8f30de-80cb-421d-b7c9-28df23819a9e-153464.png)![1653659826308-3a58ba10-ec35-485b-9781-8c15598f1302.png](./img/DaV-vhgy2BzJ45hC/1653659826308-3a58ba10-ec35-485b-9781-8c15598f1302-382286.png)



好了，可以去各个群水群发截图了！

## 2.14 打字统计功能
小科狗具有打字统计的功能，具体查看方式为在候选框界面鼠标右击，选择“字数统计”打开即可进入打字统计界面（下图）。

![1653659835235-ab44d51e-21c0-4cf3-ae97-ec9a8b50c353.png](./img/DaV-vhgy2BzJ45hC/1653659835235-ab44d51e-21c0-4cf3-ae97-ec9a8b50c353-873764.png)

小科狗字数统计界面包括两部分，上面部分包括日期时间、当天打字数、打字天数、合计打字数，当天打字平均速度及累计打字速度。下面统计表格为日期、当天打字数、当天击键数、当天上屏次数、当天打字时间、累计打字数（下图）。

小科狗的打字速度计算方法为：速度=字数/(时间+系数×上屏次数×(时间/击键数))，其中除系数外的参数均可在字数统计界面获取。小科狗目前默认将系数设置为4.5，经过试用，基本接近平常打字速度；但是由于个人习惯、码表等的不同，击键数以及上屏次数会有差异，如需要确定自己精确的打字速度，需要利用跟打器得出自己的打字速度，再根据上公式推出系数，至于此系数的手动修改功能，后面小科狗更新中会进行优化更新。

![1653659845247-404fadc0-b754-4b63-893e-8123136103b7.png](./img/DaV-vhgy2BzJ45hC/1653659845247-404fadc0-b754-4b63-893e-8123136103b7-603852.png)

需注意，打字统计数据保存位置可以根据需要进行更改，请打开安装文件目录下的“<font style="color:rgb(38, 38, 38);">keg.txt</font>”文档查看设置。如想保存在C盘根目录，则原设置不变，如想将文件保存于小科狗主程序文件夹中，请将“打字字数统计文件路径！=小科狗主程文件夹”中的！删除即可（下图）。

![1653659853293-13996a14-07e5-48de-8792-2fc9e8660e1a.png](./img/DaV-vhgy2BzJ45hC/1653659853293-13996a14-07e5-48de-8792-2fc9e8660e1a-305863.png)

无论SiKegDZCnt.dat保存于何处，请定时备份此文件，以防不可控原因导致打字统计数据丢失。此外，由于windows版本权限太高（如系统是企业版等），当保存于C盘根目录时，打字统计数据文件可能会重新定向到C:\Users\<USER>\AppData\Local\VirtualStore文件夹中，如果在C盘根目录下没有发现，请显示隐藏文件，如果还是没有找到，则到此目录下查看。

在替换或删除打字统计文件时，需先停止小科狗服务，方法为呼出候选框，选择退出服务端即可（下图）。

![1653659862288-ff30f9ac-14b5-4e6e-aa3c-04ef1f70e295.png](./img/DaV-vhgy2BzJ45hC/1653659862288-ff30f9ac-14b5-4e6e-aa3c-04ef1f70e295-154818.png)

如果没有退出服务端，而删除了数据统计文件，则再次打开字数统计界面时，会出现下图所示提示，甚至是统计结果不正确等。

![1653659877002-ad046aca-4c8d-4c01-bba1-5950fbcb0f92.png](./img/DaV-vhgy2BzJ45hC/1653659877002-ad046aca-4c8d-4c01-bba1-5950fbcb0f92-487854.png)

该统计功能仅能对利用小科狗输入法平台输入的字符（汉字、英文及特殊字符等）数进行精确统计，并不能统计到在英文状态下输入的英文及特殊字符，如想对其进行统计，烦请使用英文码表、特殊字符码表输入需要的英文、特殊字符等。并且需要注意，对于英文单词统计的仅为字母数量而非单词数量，如利用英文码表输入week，此为一个英文单词，字数应该算为1，但是小科狗在统计时，将其数量算作4，因此利用英文码表输入时，字数统计结果会偏大。

此外，小科狗统计字数时，只统计打字时第一个编码出现到该字/词上屏的时间，字与字之间的间隔时间不统计。如果没有上屏，则超过一分钟时不统计。

## 2.14 命令直通车（超级命令）功能
小科狗内置了命名直通车（超级命令）功能，主要依赖于windows命令提示符、脚本语言以及支持命令行操作的程序，使用方法为：在主码表或任意码表的key字段中输入编码串，在value中输入启动命令。

### 2.14.1 Windows命令提示符
例如，要直接打开百度搜索主页，则按ctrl+ins，调出手动添词对话框，在key字段中输入“bd”（可以是任意自己习惯的字符），在value中输入“::百度一下-start https://www.baidu.com”即可，其中bd为编码串，而value中最前面必须有两个英文状态下的冒号，后面跟编码串对应在候选框显示的文字，例如“百度一下”，再后面跟“-start”，其中start为cmd命令（此处只是为了启动相关的页面、打开相关的文件夹以及程序，所以仅以start命令为例进行介绍，如果想调用其它的cmd命令，请自行网络搜索具体用法），start后面空格，后再录入需要打开的网址地址，例如此处的“https://www.baidu.com”，点击应用，即可（下图）。

![1653659887590-3b44301b-6a62-48fa-9731-58cce8314376.png](./img/DaV-vhgy2BzJ45hC/1653659887590-3b44301b-6a62-48fa-9731-58cce8314376-144073.png)

显示候选框界面，右击选择“更新内存数据库”。输入bd，则出现如下图候选内容，此时按空格上屏“::百度一下”，则会直接在浏览器中打开百度搜索网址。其它网址打开方式设置相同。

![1653659895081-36702764-0cb4-4679-a37a-12afbad8e932.png](./img/DaV-vhgy2BzJ45hC/1653659895081-36702764-0cb4-4679-a37a-12afbad8e932-591322.png)

如果要打开电脑上相关的文件夹，如要打开C盘，则key中录入“dkcp”，value中录入“::打开C盘-start C:\”。更新内存数据库，输入dkcp并上屏，会直接打开C盘根目录，打开某一盘位下的某一文件夹类似。

> 打开写字板：key为xab，value为“::写字板-start write”
>
> 打开计算器：key为jsq，value为“::计算器-start calc”
>
> 打开文件夹下的小科狗助手程序：key为kgzs，value为“::科狗助手-start D:\文件夹路径\小科狗助手.exe”
>

### 2.14.2 脚本语言
小科狗也可以调用其它脚本语言达到windows命令提示符的效果，具体写法为：&**exe[|]>代码段，其中&和[|]之间的为安装文件夹下car文件夹中的脚本解译器，如果脚本解译器不在car文件夹中，则要指定明确的路径，参数用[|]隔开，

以AHK脚本为例，要打开记事本，则key字段为jsb，value字段为“::打开记事本-&AutoHotKey[|]>Run, notepad.exe”，更新内存数据库，直接输入jsb，上屏“::打开记事本”候选项，则直接打开记事本程序。要打开画板，则key字段为hb，value字段为“::打开画板-&AutoHotKey[|]>Run, msPaint.exe”，更新内存数据库，直接输入hb，上屏“::打开画板”候选项，则直接打开画板程序。

其它脚本语言同AHK相同，并且不同的脚本语言的具体使用方法，本说明不作详细说明，有需要可自行网络搜索学习，并在小科狗中尝试设置。

### 2.14.3 支持命令行操作的程序
另外，如果某一程序支持命令行操作，也可以通过小科狗调用操作。例如将一个可以播放视频、音频、图片的播放器放于car文件夹下，则可以借助小科狗直接调用该程序播放指定的音视频及图片文件，例如将开源播放器ffmpeg放于car文件夹下，在码表key中输入“bfq”，在value中输入“::播放文件-&ffmpeg.exe[|]c:\国王的演讲”。更新内存数据，此时输入bfq，选中候选框中的“::播放文件”候选项，上屏，就会直接播放C盘根目录下存放的名为“国王的演讲”的视频。

## 2.15 在线搜索功能
小科狗内置在线搜索功能，其内置搜索功能可通过以下两个方式实现：

### 2.15.1 快捷键在线搜索功能
要使用此功能，请打开安装文件夹下的“<font style="color:rgb(38, 38, 38);">keg.txt</font>”TXT文档，如下图，可可以通过F5或者ctrl+F5组合键来实现，可删除其它任意一个快捷键，但不能自定义快捷键，删除后，请重启小科狗服务即可生效。

![1653659906885-98e3bfb5-6902-40e7-b610-037b598d5c5f.png](./img/DaV-vhgy2BzJ45hC/1653659906885-98e3bfb5-6902-40e7-b610-037b598d5c5f-213309.png)

注意，“<font style="color:rgb(38, 38, 38);">keg.txt</font>”文档里面仅以百度作为示例，如果有相同网址用以搜索，可以自行添加，例如利用汉典网进行字词的查询，利用英文字典网站直接进行英文单词的查询等。请自行摸索设置或到小科狗官方交流群进行交流学习。

要实现快捷键在线搜索，首先在候选框打出需要搜索的字词（<font style="color:red;">注意，需要搜索的字词不能直接上屏，必须在候选界面中</font>），此时在候选项利用上下箭头选中需要搜索的候选项（下图上），按F5或者ctrl+F5出现在线搜索对话框（下图下），此时再按一次F5或者空格键，即可直接打开百度搜索页面，并搜索对应的字词。

![1653659915703-44e0b0ea-c543-4e4e-9b03-bd00fbd9c1b3.png](./img/DaV-vhgy2BzJ45hC/1653659915703-44e0b0ea-c543-4e4e-9b03-bd00fbd9c1b3-077941.png)

![1653659919120-08c8ca43-1ed8-4dd0-b36b-3a5c91a38e65.png](./img/DaV-vhgy2BzJ45hC/1653659919120-08c8ca43-1ed8-4dd0-b36b-3a5c91a38e65-587209.png)

### 2.15.2 利用命令直通车（超级命令）功能实现在线搜索
除了利用快捷键在线搜索外，也可以通过超级命令功能实现在线搜索功能，其用法为在 **<font style="color:#E8323C;">3.14.1 Windows命令提示符</font>**的基础上对网址进行适当的修改即可。

以百度网页搜索为例，原本码表中key字段为“bd“，value字段为“::百度一下-start https://www.baidu.com”，此时按空格只是直接打开百度搜索页面而已，如果要打开百度搜索页面的同时搜索对应的字词，需要将value字段改为“::百度一下-start https://www.baidu.com/s?wd={[s]}   0”（<font style="color:red;">注意，</font><font style="color:red;">0</font><font style="color:red;">前面的为一个制表位，即</font><font style="color:red;">tab</font>）。

例如要百度一下“尸位素餐”的意思，需要先打出该词并上屏，此时输入bd，即出现“::百度一下”的候选框（下图上），如果在第一候选，直接按空格（如果不在第一候选项，则利用候选快捷键选择），即可直接打开百度搜索页面并自动搜索尸位素餐（下图下）。（<font style="color:red;">原理为利用超级命令搜索前一次上屏的字词。</font><font style="color:black;">）</font>

![1653659948962-247e73e9-0897-4da5-9037-e937446e800d.png](./img/DaV-vhgy2BzJ45hC/1653659948962-247e73e9-0897-4da5-9037-e937446e800d-688192.png)

![1653659953751-aaef6337-f66b-4d3e-92cd-2c5c314c2a25.png](./img/DaV-vhgy2BzJ45hC/1653659953751-aaef6337-f66b-4d3e-92cd-2c5c314c2a25-738015.png)

该示例仅以百度网址为例，其它网址设置相同，请自行摸索设置即可。

现将部分命令直通车代码附录于下，直接复制使用即可：

<font style="color:#1890FF;">表1部分命令直通车（超级命令）功能</font>

| key | value | 说明 |
| :---: | :---: | :---: |
| bd | ::百度一下-start https://www.baidu.com | 直接打开百度搜索主页 |
| bd | ::百度一下(直)-start https://www.baidu.com/s?wd={[s]}   0 | 打开百度并搜索关键词 |
| xzb | ::写字板-start write | 打开电脑自带写字板 |
| jsq | ::计算器-start calc | 打开电脑自带计算器 |
| hdw | ::汉典网-start https://www.zdic.net/hans/{[s]}0 | 打开汉典网并搜索关键字词 |
| cj | ::仓颉拆分-start http://www.wb86.com/cjcx/post/{[s]}.html        0 | 搜索仓颉单字拆分 |




## 2.16 词语联想功能
小科狗具有词语联想功能，请打开小科狗码表配置界面或者小科狗配置助手，定位到“词语联想设置”项（下图）。

![1653659985941-c40f079a-53d1-4a06-aab7-3af2e11bf987.png](./img/DaV-vhgy2BzJ45hC/1653659985941-c40f079a-53d1-4a06-aab7-3af2e11bf987-742832.png)

> 《要开启词语联想吗？=》要则开启，不要则关闭。
>
> 《词语联想上屏字符串长度》，如果为1则候选项仅单字联想，如果为2则仅候选项仅词语联想，如果为3则候选项单字和词组均联。
>
> 《词语联想检索范围=》，如果为1则检索码表中key=1的项，如果=2则不检索key=1的项，设置为1或2需要先对码表进行设置，使码表中有key=1或key=2的项。如果=3则是检索整个当前打字的码表。
>
> 《词语联想要显示词语全部吗？=》选择要或不要
>
> 《词语联想只是匹配首位吗=》要则仅匹配首位，即当然用于联想的字只能位于联想到的词的首位，不要时，则当前联想的词可能位于联想到的词的任意位置。
>
> 《词语联想时标点顶屏要起作用吗=》，要则起作用，不要则联想时无法顶点顶屏。
>
> 《上屏词条精准匹配key=1*的值进行词语联想吗?=不要》，该项仅对词语联想检索范围=1时有效。
>
> 《要开启Ctrl键清联想吗?=不要》，请根据需要设置为要或不要。
>

现在仅就两种简单的设置模式以作示例说明：

**<font style="color:red;">模式一</font>****<font style="color:red;">:</font>**

![1653660009546-5a1b07a5-3583-426d-8ffc-bc96b93b4971.png](./img/DaV-vhgy2BzJ45hC/1653660009546-5a1b07a5-3583-426d-8ffc-bc96b93b4971-219444.png)

仅开启词语联想和词语全部功能，此时打用于联想的字，按空格，则会匹配联想字位于任意位置的所有候选词，如下图，当输入“此”时，按空格则会匹配到码表里面所有与“此”有关的词，此时按选重键选择需要上屏的候选项即可。

![1653660017339-00ca2d4a-168d-4c26-bf3f-ce2818e7bf71.png](./img/DaV-vhgy2BzJ45hC/1653660017339-00ca2d4a-168d-4c26-bf3f-ce2818e7bf71-155338.png)

**<font style="color:red;">模式二</font>****<font style="color:red;">:</font>**

![1653660027049-cbaa7ee4-12e1-4e94-af30-38a5809714d4.png](./img/DaV-vhgy2BzJ45hC/1653660027049-cbaa7ee4-12e1-4e94-af30-38a5809714d4-087294.png)

图86

此种模式下，仅会匹配到用于联想的词在首位的情况，例如输入”此”按空格，则其联想候选项如下图所示，仅会显示以“此”字开头的词。

![1653660034443-2dc95650-3bc0-4db3-b4a4-60fa187b18a4.png](./img/DaV-vhgy2BzJ45hC/1653660034443-2dc95650-3bc0-4db3-b4a4-60fa187b18a4-911502.png)

## 2.17 过渡态的设置
小科狗输入法可以对输入过程中的过渡态进行具体的设置，以达到丰富输入功能的效果。所谓过渡态，即为敲击引导快捷键后，未输入编码串之前，候选框所处的状态。<font style="color:red;">注意，每个内容必须置于< >内，且紧跟在所引导码表后面，否则无效，如“英文宝典<1=；><2={[s]}> <3=   > <4=  > <5=  >”每个引导码表后面可以设置9个不同的过渡态内容。</font>如下图所示，该示例对一个分号引导时设置了过渡态功能：<1=；>，对一个z引导时设置了过渡态功能：<1={[s]}>，对两个z引导时设置了过渡态功能：<1=z>。

![1653660044745-7a39d090-d989-40e3-a5ff-f24b815c0a49.png](./img/DaV-vhgy2BzJ45hC/1653660044745-7a39d090-d989-40e3-a5ff-f24b815c0a49-182420.png)

示例中各过渡态的功能详解如下：

一个分号时，如果敲击分号键，出现如图89所示过渡态状态，此时如果直接输入编码串，则进入英文宝典输入中（下图）；如果不是直接输入编码串，而是按空格，则直接上屏“；”，这就完美解决了将一个符号设置为引导键时无法直接输入该快捷键本身的问题。

![1653660055244-93e8f960-81a2-4d11-a2fd-9538585f2e8f.png](./img/DaV-vhgy2BzJ45hC/1653660055244-93e8f960-81a2-4d11-a2fd-9538585f2e8f-235403.png)![1653660059703-f4956e61-1396-4b4d-9c16-04bd6fbd97b5.png](./img/DaV-vhgy2BzJ45hC/1653660059703-f4956e61-1396-4b4d-9c16-04bd6fbd97b5-599106.png)

如下图所示，其中第1个候选项即为一个z时引导的86五笔生僻字码表，而第2个候选项即为过渡态内容，此时如果按正常的输入习惯输入编码串，即进入生僻字输入，如下图左所示，如果不是输入编码串，而是直接按空格键，则默认起作用的为第2个候选项，该候选项起到重复上屏的功能（前文中的{[s]}本身起到重复上屏功能）（**<font style="color:#DF2A3F;">注意，想要过渡态中重复上屏功能起作用，例如本设置为一个z时，则需要在设置中的“大键盘码元”中将z键删除，否则重复上屏功能无效</font>**），此时则不需要在**<font style="color:#E8323C;">2.9 重复上屏设置</font>**<font style="color:black;">或</font>**<font style="color:#E8323C;">3.2 其它输入习惯设置重</font>**复上屏码元字符串中进行重复上屏设置了（留空不设置即可）。

![1653660093274-5fd472fd-75ab-49cd-a6bf-90697cf92743.png](./img/DaV-vhgy2BzJ45hC/1653660093274-5fd472fd-75ab-49cd-a6bf-90697cf92743-307463.png)![1653660096463-0daadc8c-b781-4bd5-9e2c-c3b64163f985.png](./img/DaV-vhgy2BzJ45hC/1653660096463-0daadc8c-b781-4bd5-9e2c-c3b64163f985-494895.png)

<font style="color:red;">注意，任何码表引导快捷键均可设置其为重复上屏功能。</font>例如上文中，将一个分号设置为重复上屏功能，则如下图上所示，此时如果敲击一下分号，则过渡状态如下图下所示，此时输入编码串，则直接进入英文宝典输入状态，如果设空格，则直接上屏“；”，如果用选重快捷键选择第3个候选项，则是重复上屏功能。

![1653660105484-24472c2c-cc78-412a-a75c-a5bcd147b1c5.png](./img/DaV-vhgy2BzJ45hC/1653660105484-24472c2c-cc78-412a-a75c-a5bcd147b1c5-335836.png)

![1653660109841-353e5085-68b5-4730-95b6-42cce7b09791.png](./img/DaV-vhgy2BzJ45hC/1653660109841-353e5085-68b5-4730-95b6-42cce7b09791-428507.png)

如下图左所示，为两个z时的过渡态状态，此时如果直接输入编码串，则进入符号码表输入状态（下图右），如果敲击空格，则直接上屏小写字母z。

![1653660118867-13b9214e-cad6-485c-a90f-268693e8996c.png](./img/DaV-vhgy2BzJ45hC/1653660118867-13b9214e-cad6-485c-a90f-268693e8996c-506797.png)![1653660122262-bc5cf5de-a2b4-4a47-bfe5-13928b5b4690.png](./img/DaV-vhgy2BzJ45hC/1653660122262-bc5cf5de-a2b4-4a47-bfe5-13928b5b4690-825855.png)

---

## 2.18 metro界面置顶
新添加置顶版小科狗安装程序，解决在metro界面无法置顶的问题。因为小科狗置顶功能是在服务端中实现，而其它输入法的置顶功能是在tsf中实现，因此，<font style="color:#DF2A3F;">一旦安装置顶版小科狗，每次启动的时候就会被系统检查，跳出对话框出来，请根据需要选择是否试用置顶版小科狗。</font>

<font style="color:#DF2A3F;">注意：需要置顶功能起作用，1）请进行签名（具体操作参见1.1节小科狗安装的相关内容）；2）小科狗安装文件夹必须放在系统文件夹中。</font>

 经过系统证书检查，认为小狗的服务端是被认证的，又是系统启动的，于是界面权限得到提升，升到metro界面上面来了。

---

## 2.19 标点设置
小科狗标点有四个设置项，具体请看配置界面的标点设置及动作设置项：

![1683291851116-538529cd-70ed-40ec-bed4-9925cf4138ca.png](./img/DaV-vhgy2BzJ45hC/1683291851116-538529cd-70ed-40ec-bed4-9925cf4138ca-841572.png)

![1683292109083-d8b064ea-5a11-453e-94a7-7df5cfebd0ac.png](./img/DaV-vhgy2BzJ45hC/1683292109083-d8b064ea-5a11-453e-94a7-7df5cfebd0ac-651899.png)

### 2.19.1 中英文标点关联
动作设置《关联中文标点吗？》，要，则所有标点符号均为中文标点符号，不要，则为英文状态下的标点符号。**<font style="color:#DF2A3F;">建议：中文码表时，可以设置为要，英文码表可以设置为不要，具体请根据需要自行设置。</font>**

### 2.19.2 数字后标点自动转点
动作设置《前次上屏的是数字再上屏句号*要转成点号*吗?=要》，改为要时，输入数字后，直接输入句号时，句号会自动转化为点。

如果是在关联中文标点的状态下，第一次输入时为句号，此时如果回删，再次输入句号，则句号自动变为点号，再次回删，输入句号，则句号依然为句号。也就是说回删操作使得句号在[。]和[.]之间不断切换。

### 2.19.3 标点符号锁定功能。
**<font style="color:#DF2A3F;">小科狗输入法平台各码表之间中英文标点符号状态互不影响，相互独立。</font>**

而标点符号锁定功能则是锁定标点符号后，不管切换到哪个方案码表，标点符号状态均以锁定选项为准，不随码表方案的切换而改变。

![1676777408676-b9fc820e-b1c0-4ec0-bdb5-cc2bd6d8fb65.png](./img/DaV-vhgy2BzJ45hC/1676777408676-b9fc820e-b1c0-4ec0-bdb5-cc2bd6d8fb65-150012.png)

具体设置方法：呼出候选框，鼠标右键单击调出来设置界面，选择”标点符号锁定“，此时会有三个选项：”英文符号“、”中文符号“和”自动不锁“。选择”英文状态“，则所有码表的标点符号状态均为英文状态；选择”中文符号“，则所有码表的标点符号状态均为中文状态；选择”自动不锁“，则标点符号锁定功能无效，此时中英文状态受”3.19.1节当中所介绍的设置项决定。

此功能的优点为： 例如，当使用现代五笔打字时，应该用的是中文标点，但可强制用英文标点；  又比如将现代五笔切换英文状，但打英文用现代五笔的中文标点状态等等。请使用者在使用当中仔细体会其功能上的差异，并找到相适应的使用场景。

**<font style="color:#DF2A3F;">建议</font>**：初次使用小科狗时，请先选择”自动不锁“选项，等后期熟悉各个配置后，再尝试使用另外两个选项的功能。

### 2.19.4 中文标点串
中文标点串有两个设置项，分别为在按下和不按下Shift键进的中文标点输出，具体参见配置界面配置项，请自行配置。建议初次使用小科狗时默认即可，熟悉小科狗各项功能后，再做尝试。

### <font style="color:rgb(25, 27, 31);">2.19.5 标点自动配对</font>
<font style="color:rgb(25, 27, 31);">小科狗具有标点自动配对功能，设置步骤如下：</font>

<font style="color:rgb(25, 27, 31);">1）右击小科狗状态栏，选择“标点符号锁定与自动匹配”，勾选“自动匹配”。</font>

![1743066296009-5146b1bd-2725-402d-8783-06da1e3662d2.png](./img/DaV-vhgy2BzJ45hC/1743066296009-5146b1bd-2725-402d-8783-06da1e3662d2-630128.png)

<font style="color:rgb(25, 27, 31);">2）打开方案配置界面，在“全局设置”—《标点自动匹配快捷》中设置标点匹配的快捷键。设置好后，点击应用后并关闭配置界面。可通过快捷键来开启或关闭标点自动匹配功能。会有提示框提示“已打开标点自动配对”或“已关闭标点自动配对”。</font>

![1743066296635-94b128f1-9f7b-4ca1-b560-fc2cab1d1597.png](./img/DaV-vhgy2BzJ45hC/1743066296635-94b128f1-9f7b-4ca1-b560-fc2cab1d1597-697402.png)

<font style="color:rgb(25, 27, 31);">3）打开配置界面，定位到“标点设置项”，在此设置需要自动配对的标点符号。</font>

![1743066297670-99300b6e-c6d5-47c3-ac5e-bc41bf215380.png](./img/DaV-vhgy2BzJ45hC/1743066297670-99300b6e-c6d5-47c3-ac5e-bc41bf215380-058419.png)

<font style="color:rgb(25, 27, 31);">小科狗输入法一共可设置26对标点。成对标点符号中间用-链接。</font>

<font style="color:rgb(25, 27, 31);">例如：默认设置当中的：</font>

<font style="color:rgb(25, 27, 31);"><1=“-”>，表示中文双引号自动匹配。</font>

<font style="color:rgb(25, 27, 31);"><2=（-）>，表示中文括号自动匹配。</font>

<font style="color:rgb(25, 27, 31);"><3=+[-]+>，表示中文书名号自动匹配。</font>

<font style="color:rgb(25, 27, 31);"><4=-[-]->，表示英文书名号自动匹配。</font>

<font style="color:rgb(25, 27, 31);"><5={-}>，表示花括号自动匹配。</font>

<font style="color:rgb(25, 27, 31);">其它以此类推，使用者根据需要自行设置。</font>

<font style="color:rgb(25, 27, 31);">另外，小科狗标点自动配对功能支持两个标点符号的自动配对，例如：</font>

<font style="color:rgb(25, 27, 31);">设置为<6=：“-。”>，表示当打出</font>**<font style="color:#DF2A3F;">：“</font>**<font style="color:rgb(25, 27, 31);">时（即冒号和中文双引号的前引号），可自动配置句号和中文双引号的后引号</font>**<font style="color:#DF2A3F;">。”</font>**。

**<font style="color:rgb(25, 27, 31);">注意：</font>**<font style="color:rgb(25, 27, 31);">要实现两个标点符号的自动匹配，需要引导的两个标点符号同时上屏，例如上例当中的冒号和中文双引号的前引号，如果先上屏冒号，再上屏中文双引号的前引号，则自动匹配功能不起作用。因为要在码表当中将需要自动匹配的两个符号写入，以方便输入，如上例中符号在码表中我自己设置如下图：</font>

![1743066296060-392b0b95-e627-4925-82f4-0fee1eb4576b.png](./img/DaV-vhgy2BzJ45hC/1743066296060-392b0b95-e627-4925-82f4-0fee1eb4576b-007077.png)

## 2.20 双检索功能
小科狗可以进行双检索输入，具体参见“码表调频与检索设置”中的相关配置项：

![1683293628226-0e748aa9-d47d-4f5f-b77b-68be2ae29acd.png](./img/DaV-vhgy2BzJ45hC/1683293628226-0e748aa9-d47d-4f5f-b77b-68be2ae29acd-430653.png)

双检索共有两项设置，其一为是否启用双检索，其二是双检索时编码是否完全匹配。请开启双检索自行尝试。

举个栗子说明双检索实现方式及效果：当上屏“你”后，利用双检索检索到“好啊”并直接上屏，则key应该为“haoa”（此处的key值可以随意设置，前提是在进行双检索时，你能记得这个key值，例如只设置为ha也可以），value应该为“好啊>#你#”（见下图手动添加词界面）。此时，如果打出“你”上屏，再打“haoa”，则“好啊”会自动上屏补全到“你”后面，形成词组“你好啊”（见下动态示意图）。

![1683294035730-f616604c-431e-4f7d-a220-2f58a8166032.png](./img/DaV-vhgy2BzJ45hC/1683294035730-f616604c-431e-4f7d-a220-2f58a8166032-448699.png)

![1683294846437-684eafe7-72d1-4821-93da-acaab2627fac.gif](./img/DaV-vhgy2BzJ45hC/1683294846437-684eafe7-72d1-4821-93da-acaab2627fac-377358.gif)



## 2.21 英文整句功能
要使用英语整句功能，首先需要在设置里面启用临时英文长句功能。具体在“动作设置”中的“Shift键+字母键要进入临时英文长句态吗》”，将面改为“要“即开启英文整句功能。

![1685447862790-002ac621-88ad-473f-aaa4-e984930b29b8.png](./img/DaV-vhgy2BzJ45hC/1685447862790-002ac621-88ad-473f-aaa4-e984930b29b8-704212.png)

开启英文整句功能的前提下，按下Shift键的同时，再按符号键位，即可进入英文整句输入状态，此时英文整句的首字母为大写。在英文整句状态下，空格按不上屏，而是直接在编码串处输入空格，此时如果要上屏编码串，请按回车键。当回车上屏编码串后，即退出英文整句功能状态。

![1685448075267-540292de-6d19-4a2b-96bc-0f541878f000.png](./img/DaV-vhgy2BzJ45hC/1685448075267-540292de-6d19-4a2b-96bc-0f541878f000-451600.png)

如果不需要英文整句状态下首字母为小写，则需要将英文整句功能上方的“shift键上屏编码串吗”后面改为“不要”即可（上上图所示）。

如果先打一两个码元，让候选框编码串处于显示状态，此时按**<font style="color:#DF2A3F;">左shift</font>**的同时，再按其它按键，则此时输入的字母为大写，松开shift键继续输入小写编码串即可，如果要再次输入大写字母等，则再次按住shift键输入即可，候选框编码串为如下图所示，其中L、T、U和X均为在按住shift键的同时输入的码元。

![1685449208982-c93e7dfa-c88d-44fe-8ec1-53565cb854b2.png](./img/DaV-vhgy2BzJ45hC/1685449208982-c93e7dfa-c88d-44fe-8ec1-53565cb854b2-960467.png)

<font style="color:rgb(38, 38, 38);">如果先打一两个码元，让候选框编码串处于显示状态，此时按</font>**<font style="color:rgb(223, 42, 63);">右shift</font>**<font style="color:rgb(38, 38, 38);">的同时，再按其它按键，则此时输入的字母均为小写状态，如下图所示。</font>

![1685449423821-0eeb7420-5719-4b9e-864b-eb70fb3373fe.png](./img/DaV-vhgy2BzJ45hC/1685449423821-0eeb7420-5719-4b9e-864b-eb70fb3373fe-660348.png)

**<font style="color:#DF2A3F;">注意</font>**，当第一次上屏编码串时，按左Shift为大写，按右Shift为小写，而第二次及以后无论按左还是右Shift均为大写。

## 2.22 临时计算功能
小科狗具有临时计算的功能，具体设置为：请开启临时英文长句功能（下图上），按住Shift的同时按G（角度）或H（弧度）键，进入英文长句状态，再按=，此时输入需要计算的功能即可（下图下）。

<font style="color:#DF2A3F;">注意，小科狗只能进行简单的数据运算，具体包括哪些，请自行尝试。</font>

![1690713624000-d527b8e4-cd24-4fcd-9b3f-911bcddf76b8.png](./img/DaV-vhgy2BzJ45hC/1690713624000-d527b8e4-cd24-4fcd-9b3f-911bcddf76b8-064953.png)

![1690773526279-7a7f9a34-f330-461f-a817-cbd536210f9b.png](./img/DaV-vhgy2BzJ45hC/1690773526279-7a7f9a34-f330-461f-a817-cbd536210f9b-118636.png)![1690773638268-6823fba5-d6f0-44d3-9406-f2ba9607e415.png](./img/DaV-vhgy2BzJ45hC/1690773638268-6823fba5-d6f0-44d3-9406-f2ba9607e415-734863.png)![1690773687460-16db2d54-9e34-4a1c-93e1-535370b333e5.png](./img/DaV-vhgy2BzJ45hC/1690773687460-16db2d54-9e34-4a1c-93e1-535370b333e5-185318.png)

## 2.23 数字大写转化
<font style="color:rgb(38, 38, 38);">        小科狗具有数字转化为大写的功能，具体设置为：请开启临时英文长句功能（见3.22），按住Shift的同时按Q，进入英文长句状态，再按数字，此时数字即被转化为大写，见下图：</font>

![1699236816265-29d4abaa-fbb4-4ca2-836a-c479f3d356e4.png](./img/DaV-vhgy2BzJ45hC/1699236816265-29d4abaa-fbb4-4ca2-836a-c479f3d356e4-286534.png)

![1699236847723-3967da12-707c-4ea1-ad9e-e30c0e7b8cfc.png](./img/DaV-vhgy2BzJ45hC/1699236847723-3967da12-707c-4ea1-ad9e-e30c0e7b8cfc-088292.png)

## 2.24 字典查询功能
小科狗具备英译汉和汉译英的字典查询功能，其使用方法如下：

下载最新版小科狗安装包安装，在小科狗下载位置下载”典库查询“文件夹，可解压放于任意位置，本说明将其放于小科狗安装包的tools文件夹中。

![1707028560116-491be2bd-b02c-437b-9873-ca4fea561cd6.png](./img/DaV-vhgy2BzJ45hC/1707028560116-491be2bd-b02c-437b-9873-ca4fea561cd6-483463.png)

其中fcTip词.ahk为配置文件，懂ahk的，可打开自行进行相关配置的自定义，kegDic.db为字典码表，可打开查看，也可将自己需要使用的码表导入进去，具体操作方法，请看前面相关章节说明。<font style="color:#DF2A3F;">建议初学者所有文件默认即可。</font>

打开小科狗安装包中的keg.txt，将下面文字复制粘贴于任意位置，

> 《运行命令行快键=<1=LCT><2=F12><3=><4=><命令行=E:\小科狗\tools\典库查询\fcTip词.exe&&&E:\小科狗\tools\典库查询\fcTip词.ahk&&&汉译英&&&[s]>》
>
> 《运行命令行快键=<1=LCT><2=F11><3=><4=><命令行=E:\小科狗\tools\典库查询\fcTip词.exe&&&E:\小科狗\tools\典库查询\fcTip词.ahk&&&英译汉&&&[s]>》
>

依照上面的码表存储路径，将其中的路径改为你自己电脑“典库查询”中字典码表所在位置。

其中，汉译英的快捷键为左ctlr+F12，英译汉的快捷键为左ctrl+F11，可进行自定义，具体操作方法请参见本说明最前面的小科狗快捷键自定义说明。

### 2.24.1 汉译英功能
当候选框显示候选项后，将光标移到到需要翻译的候选项上，按左ctlr+F12即可翻译，并在右侧单独的窗口显示。<font style="color:#DF2A3F;">注意，翻译时，候选框必须显示，如果为编码唯一则直接上屏，候选框消失，则无法进行翻译。</font>

![1707028987273-32051037-3a4b-4782-a5ba-f0b122eb7a6b.png](./img/DaV-vhgy2BzJ45hC/1707028987273-32051037-3a4b-4782-a5ba-f0b122eb7a6b-139110.png)

![1707029012366-63971675-41fe-4fe9-91fa-eebc1cd72efe.png](./img/DaV-vhgy2BzJ45hC/1707029012366-63971675-41fe-4fe9-91fa-eebc1cd72efe-691748.png)

### 2.24.2 英译汉功能
实现方法同2.24.1，快捷键为左ctrl+F11。

![1707029136713-f58ed80d-6e8f-4574-8346-0f05fd34630c.png](./img/DaV-vhgy2BzJ45hC/1707029136713-f58ed80d-6e8f-4574-8346-0f05fd34630c-308124.png)

![1707029219324-d2c2901b-c1b9-4de1-bb73-0a0103cb4410.png](./img/DaV-vhgy2BzJ45hC/1707029219324-d2c2901b-c1b9-4de1-bb73-0a0103cb4410-744654.png)

![1707029273375-4cbaaf76-ae15-4594-aac0-128e87307bdd.png](./img/DaV-vhgy2BzJ45hC/1707029273375-4cbaaf76-ae15-4594-aac0-128e87307bdd-131105.png)

**<font style="color:#DF2A3F;">注意，字典查询候选框按空格或esc键消失。</font>**

**<font style="color:#DF2A3F;">如果不需要前面的序号，可在配置后面添加：</font>****<font style="color:rgb(223, 42, 63);">显示编号:0，例如：</font>****<font style="color:#DF2A3F;">《运行命令行快键=<1=LCT><2=F3><3=> <4=><命令行=E:\小科狗\tools\典库查询\fcTip词.exe&&&E:\小科狗\tools\典库查询\fcTip词.ahk&&&英译汉&&&[s]&&&显示编号:0>》，结果如下：</font>**

![1707198135282-ae9af0df-a273-4e71-9193-febc3f39b64a.png](./img/DaV-vhgy2BzJ45hC/1707198135282-ae9af0df-a273-4e71-9193-febc3f39b64a-400670.png)

<font style="color:#DF2A3F;">实际上该字典翻译功能，并不局限于字典的翻译功能，可实现其它的例如：简繁转化查询、拆分查询、卡片学习、诗词查询等功能，请喜欢折腾的制作相关码表分享。</font>

![1707198191341-37ce6898-1872-4ad4-a2ce-141bc2a5a646.png](./img/DaV-vhgy2BzJ45hC/1707198191341-37ce6898-1872-4ad4-a2ce-141bc2a5a646-655481.png) 

## 2.25 其他功能（待补充）
2025-05-21，开发者提到，本文档还缺乏对如下功能的说明，先罗列于此。

### 2.25.1  手写反查
### 2.25.2 内存映射反查
### 2.25.3 整句码表和大词库码表的快速导入导出
### 2.25.4 自动关机、自动锁屏以及间隔锁屏    
 
---

## <font style="color:rgb(25, 27, 31);">3 相关问题汇总及解决办法</font>
### <font style="color:rgb(25, 27, 31);">3.1 配置项的保存</font>
<font style="color:rgb(25, 27, 31);">1）当对各个码表的配置进行修改以后，请呼出输入候选界面，选择保存内存数据库，或者此后不再进行修改，关机或者注销电脑时，会自动保存。</font>

<font style="color:rgb(25, 27, 31);">2）在对数据库字词进行修改以后，请呼出输入候选界面，选择更新内存数据库。</font>

<font style="color:rgb(25, 27, 31);">3）如果同时对码表配置内容和数据库字词内容进行了修改，请右击菜单中选择“保存内存数据库到硬盘”，即可将所做更改保存到码表数据库当中，如果选择“更新内存数据库自硬盘”，则是将码表数据库中的配置更新到当前设置中，请自行操作体验。另外，不管是对配置还是码表数据库所做更改在关机时，会自动保存到码表数据库中，但是由于电脑差异，可能自动保存不成功，需要不定时手动保存，以免前面所做更改丢失。因此在修改配置或者修改数据库时格外注意，以免重复修改配置，造成不必要的时间浪费。</font>

<font style="color:rgb(25, 27, 31);">4）小科狗输入法各码表的配置存储于各码表中，并非存储于软件当中，因此如果要在其它电脑使用当前配置的码表，直接复制Keg.db即可。各码表之间的配置是相互独立的，所以不同码表之间可以根据自己的要求设置不同的输入法习惯、界面样式等，甚至同一码表可以命名成不同的名字，设置成不同的使用习惯来进行使用。</font>

### <font style="color:rgb(25, 27, 31);">3.2 码表权重的设置</font>
<font style="color:rgb(25, 27, 31);">在码表配置界面如果选择权重全置0，则该码表的权重列（weight）全部变为0，此时再配置调频功能，对各字词权重进行自动或者手动调整即可。某些码表如果已经根据使用习惯将权重养成自己使用的频率，则请慎用此功能，因为一旦重置为0，则不可恢复，需要在平时重新根据使用习惯慢慢养成调频权重，或者通过其它软件进行重新调整。</font>

### <font style="color:rgb(25, 27, 31);">3.3 窗口置顶问题</font>
<font style="color:rgb(25, 27, 31);">目前小科狗候选框无法在某些界面置顶，例如Windows10/11的搜索框，某些界面无法进行中文输入，例如腾讯软件管理界面的搜索框等，这是由于其权限问题所致，也是除大厂以外大部分小众输入法所面临的问题，请使用小科狗者悉知。</font>

### <font style="color:rgb(25, 27, 31);">3.4 联网及数据同步</font>
<font style="color:rgb(25, 27, 31);">小科狗为本地输入法平台，没有联网功能，因此并不能实现通过网络同步配置、词库等功能，请自行斟酌使用，如使用，请做好备份，以免数据库或配置丢失，造成不必要的麻烦。如想同步，请使用网盘工具进行备份同步，具体操作烦请自行网络搜索。</font>

### <font style="color:rgb(25, 27, 31);">3.5 快捷键设置</font>
<font style="color:rgb(25, 27, 31);">使用笔记本电脑用户请格外注意，某些品牌笔记本电脑内置快捷键操作功能，例如将F1-F12内置成了多媒体快捷键功能，因此直接利用F9-F11在小狼毫、大科狗之间切换时，由于快捷键冲突，可能并不会起作用，此时，需要进入电脑BIOS中对电脑的内置快捷键进行设置，具体设置方法烦请自行网络搜索；如果没有对内置快捷键进行设置前切换到小狼毫输入法，则由于快捷键冲突无法切换回小科狗输入法，此时有两种办法解决，其一是卸载小科狗，重新安装即可，其二是尝试删除C:\Windows\TSF\SiKegInput下的两个文件。</font>

### <font style="color:rgb(25, 27, 31);">3.6 服务启动失败</font>
<font style="color:rgb(25, 27, 31);">1）如果安装小科狗之后，输入时提示“小科狗服务端打开失败”，请打开安装目录，双击KegServer.exe服务文件，启动小科狗服务即可。如问题依旧，请使用终极大法：重新安装小科狗。</font>

<font style="color:rgb(25, 27, 31);">2）如果电脑启动后，只能上屏英文字母，说明小科狗服务端没有随电脑自启动，有以下解决办法：（1）打开小科狗安装文件夹，双击KegServer.exe启动小科狗服务，这个办法比较心烦，因为每次电脑启动都要点击启动一下，彻底的解决办法请看（2）；（2）请用记事本打开安装文件夹中的install.bat文件，将“rem sc config %sname% start=delayed-auto”改为“rem sc config %sname% start=auto”即可。如果还是无法自启动，请百度之，将小科狗服务端（KegServer.exe）添加到电脑自启动中。</font>

### <font style="color:rgb(25, 27, 31);">3.7 访问未命名的文件时尝试越过其结尾</font>
<font style="color:rgb(25, 27, 31);">提示“访问未命名的文件时尝试越过其结尾”，退出小科狗服务后，删除C盘SiKegInput文件夹下面的PreCodeName.dat文件即可。</font>

### <font style="color:rgb(25, 27, 31);">3.8 安装到C盘的注意事项</font>
<font style="color:rgb(25, 27, 31);">如果安装在C盘，则需要将下图所示的几个文件放到C盘的SiKegInput文件夹下，否则开启了uac权限，DB码表文件只有读取而没有写入的权限。</font>

<font style="color:rgb(25, 27, 31);">  
</font>

![1740446993325-467d1a93-be74-407c-a31f-107085ab29e3.png](./img/DaV-vhgy2BzJ45hC/1740446993325-467d1a93-be74-407c-a31f-107085ab29e3-241097.png)

添加图片注释，不超过 140 字（可选）

### <font style="color:rgb(25, 27, 31);">3.9 字体安装</font>
<font style="color:rgb(25, 27, 31);">安装字体时，如果小狗没有调用这个字体，就直接安装，如果小狗调用了这个字体退出小科狗服务端再安装。</font>

### <font style="color:rgb(25, 27, 31);">3.10 打字时突然卡死，服务重启</font>
<font style="color:rgb(25, 27, 31);">有时候打字，突然就卡死了，而且只能把编码上屏，等一会儿小科狗服务端会再次启动，才能正常打字。这通常是通讯方式引起的问题，如果遇到此种情况，请按ctrl+F7，启用消息内存通讯，即可解决问题。</font>

---

<font style="color:rgb(25, 27, 31);">  
</font>

---



# 感谢及其它
小科狗输入法平台融合了小狼毫、影子和大科狗等输入法及平台的特点。在此特别感谢小狼毫与影子输入法平台的开发者。同时感谢软件开发过程中提出宝贵意见的QQ官方交流群各位仁兄，小科狗的完善、成长离不开你们的支持!

该输入法为「李子哥」个人独立开发产品，欢迎大家分发传播使用，~~但请勿用于商业用途~~<font style="color:#AE146E;">（xβ注：这段感谢及其它，并非开发者所写，而是文档创建者毛小驴所写。2025-05-22 开发者在QQ群更正此表述为→）</font>**<font style="color:#DF2A3F;">可以任意用于任何商业用途</font>**。本软件开发者申明该软件不包含任何恶意内容，不对本软件的安装、使用提供任何的安全保证。一切版本以官方QQ群发布为准。不对软件使用过程中所遇到的任何理论或实际上的损失承担责任。请自行斟酌使用。

---

**小科狗铁粉：毛小驴    编辑**

---

# 附录
## 小科狗更新日志
**<font style="color:rgb(25, 27, 31);">2025年5月16日</font>**

> <font style="color:rgb(83, 88, 97);">增加状态栏缩放和隐藏方案名选项，具体见配置界面之“全局设置”相关配置项。</font>
>
> <font style="color:rgb(83, 88, 97);">增加编码串序号自定义、码表标签色、主副码表标识色</font>
>

**<font style="color:rgb(25, 27, 31);">2025年5月12日</font>**

> <font style="color:rgb(25, 27, 31);">修改小狗截图全局命令默认快键；优化小狗中在英雄联盟游戏中英文切换的问题。</font>
>

**<font style="color:rgb(25, 27, 31);">2025年5月7日</font>**

> <font style="color:rgb(25, 27, 31);">匹配皮肤文件默认项：如果没有发现Keg.gif文件，会寻找Keg.png文件，如果还没有，会报错。</font>
>

**<font style="color:rgb(25, 27, 31);">2025年4月26日</font>**

> <font style="color:rgb(83, 88, 97);">增加皮肤菜单</font>
>
> <font style="color:rgb(83, 88, 97);">增加间隔锁屏</font>
>

**<font style="color:rgb(25, 27, 31);">2025年4月9日</font>**

> <font style="color:rgb(83, 88, 97);">增加“候选到边框线的间距”配置项。</font>
>

**<font style="color:rgb(25, 27, 31);">2025年4月7日</font>**

> <font style="color:rgb(83, 88, 97);">增加“候选窗口候选排列方向模式>1时隐藏编码串行时无候选时，编码转候选1吗？=不要》”设置项。</font>
>

**<font style="color:rgb(25, 27, 31);">2025年3月31日</font>**

> 默认自动为消息内存通讯，如消息内存不起作用，则自动切换到命名管道通讯。
>
> 优化自动造词功能
>

**<font style="color:rgb(25, 27, 31);">2025年3月23日</font>**

> 修复在Adobe家的PDF软件无法打字上屏的问题
>

**<font style="color:rgb(25, 27, 31);">2025年3月15日</font>**

> <font style="color:rgb(25, 27, 31);">增加支持中英文标点符号自动配对,最大27对</font>
>

**<font style="color:rgb(25, 27, 31);">2025年3月12日</font>**

> 更新因改了通讯接口，需要重新安装，更新tsf夹，更新服务端，更新配置端
>
> 支持嵌入字体色自定义
>

**<font style="color:rgb(25, 27, 31);">2025年3月8日</font>**

> <font style="color:rgb(83, 88, 97);">优化按键监控  
</font><font style="color:rgb(83, 88, 97);">增加键盘按键全局钩子跟tsf按键事件相互配合</font>
>

**<font style="color:rgb(25, 27, 31);">2025年3月3日</font>**

> 优化检测键按下的代码
>

**2025年3月1日**

> <font style="color:rgb(83, 88, 97);">修复重复上屏跟引导打字字数的统计问题</font>
>

**2025年2月26日**

> 增加大写状态，自动标点下为英文标点，增加自动标点提示
>

**2025年2月24日**

> 因语雀分享链接有分享时间限制，故同时开通知乎在线说明；
>
> 右键菜单添加知乎在线文字说明。
>

**2025年2月13日**

> 新增状态栏右键菜单皮肤设置入口
>

**<font style="color:rgb(25, 27, 31);">2025年1月23日</font>**

> 增加截图上屏时字体颜色和背景色自定义功能
>

**2024年3月26日**

> 小科狗配置GUI工具发布。请于下载链接下载试用。
>

**2024年3月24日**

> 智能光标跟随优化。
>

**2024年3月21日**

>  整句模式支持修改词条 。
>

**2024年3月19日**

> office软件搜索界面优化
>

**2024年3月14日**

> UAC相关优化。
>

**2024年3月13日**

> 相关优化。
>

**2024年2月28日**

> 修复三种检索命令直通车；优化过渡态上屏。
>

**2024年2月4日**

> 添加字典查询功能，具体使用方法详见2.24节。
>

**2023年12月19日**

> 添加状态栏皮肤文字位置定义功能，详见2.1.2节。
>

**2023年12月18日**

> 添加嵌入时下划线颜色自定义功能。
>

**2023年12月16日**

> 添加自定义皮肤功能，支持静态和动态皮肤，具体配置内2.1.2节。
>

**2023年12月10日**

> 从小儿狼狗切换到其它输入法时，状态条隐藏。
>

**2023年12月9日**

> 候选框选中项底色添加圆角效果。
>

**2023年12月8日**

>  分离检索，异步检索 。
>

**2023年12月6日**

> 添加码表页翻页提示。
>
> 优化小启跟打器兼容问题。
>

**2023年11月30日**

> 优化增量配置。
>
> 添加中英文切换快捷键。
>
> 添加自动颠倒开关。
>

**2023年11月29日**

> 候选框圆角可以进行独立设置。
>

**2023年11月19日**

>  升级命名管道为十线程，设两个空闲等待线程。
>

**2023年11月6日**

> 添加数字大写转化功能，具体参看2.23节。
>

**2023年10月22日**

> 优化全局设置与方案设置之间的切换逻辑。
>
> 修改“使用说明.txt”为“keg.txt”，以兼容英文系统。
>

**2023年10月18日**

> 大小写优化。
>

**2023年10月16日**

> 常规优化。
>

**2023年9月22日**

>  增加手动设置tsf跟随快键 。
>

**2023年8月29日**

> 优化光标智能跟随。
>

**2023年8月25日**

> 添加引导翻页功能。
>

**2023年7月30日**

> 添加临时计算功能。具体使用方法参见2.22节。
>

**2023年7月8日**

> 增加候选框单行、双行和多行配置功能。
>

**2023年6月24日**

> 添加嵌入式功能。
>

**2023年6月19日**

> 添加码表导出功能；
>
> 状态栏右键菜单集成。
>

**2023年6月3日**

> 添加快捷键自定义功能；
>
> 多屏光标跟随优化。
>

**2023年5月30日**

> 相关优化。
>

**2023年5月28日**

> 添加临时英文长句功能。
>

**2023年5月26日**

> 优化光标跟随。
>

**2023年5月22日**

> 优化候选窗口显示：全面采用透明度显示或隐藏候选窗口。
>
> 优化顶屏、选重逻辑。
>

**2023年5月21日**

> 优化双检索速度。
>
> 修复标点顶屏和数字顶屏  
>

**2023年5月20日**

> 优化十重历史双检索，解决双检索初始化循环检索问题。
>

**2023年5月19日**

> 支持两重历史双检索；
>
> 双检索增加十重历史，支持全自定义历史重数；
>
> 修复上下箭头移动功能；
>
> 修复方案选择时数字键选择功能；
>

**2023年5月17日**

> 优化重复上屏问题；
>

**2023年5月15日**

> 方案选择时，可以通过空格或回车键选择确认码表议案；
>
> 优化右键菜单打开配置窗口卡的问题。
>

**2023年5月14日**

> 代码、性能优化；
>
> 添加方案选择时，按ins上屏方案名功能。
>

**2023年5月13日**

> 状态栏优化；
>
> 限流电报码；
>
> 加入显示指示双向队列。
>

**2023年5月12日**

>  限制部分电报码进入命名管道；随进程焦点快速显示进程的方案的状态。
>

**2023年5月8日**

> 候选框而已优化调整。
>

**2023年5月7日**

> 优化状态栏；
>
> 候选框编码串前面支持Emoji显示；
>
> 候选框添加支持中英文状态显示。
>

**2023年5月5日**

> 添加大键盘中文标点串配置项，详见2.2.5节说明。
>

**2023年5月3日**

> 添加状态栏，具体设置参考2.1.2节说明；
>
> 添加中英文切换设置，具体请参考2.2.4节说明；
>
> 其它优化。
>

**2023年4月28日**

> 添加Ctrl+加减号调整候选框大小；当候选框最小化一定程度后自动隐藏，达到盲打效果。
>
> 其它优化。
>

**2023年4月27日**

> 优化候选框闪烁问题。
>

**2023年4月26日**

>  修正：剪切板占用问题；自造词会在key插入空格问题。
>

**2023年4月25日**

> 配置界面优化；字体绘制优化。
>

**2023年4月16日**

> 支持Emoji符号彩色显示；字体回退优化。具体设置，请看对应章节说明。
>

**2023年3月25日**

> 代码优化。
>

**2023年3月6日**

> 小科狗可以开机自启动，请安装QQ群最新版本即可。
>

**2023年2月19日**

> 增加标点符号锁定功能。详细说明请参见说明中的2.19节。
>

**2023年2月18日**

> 新添加置顶版安装程序，需要metro界面置顶的请安装试用。具体设置请参见说明中的2.18节。
>

**2023年2月17日**

> 支持百万级词库。
>

**2023年2月8日**

> 修复CAD上屏相关问题；优化：可更改所有文件放于安装文件夹目录，详见安装文件夹中的“使用说明”。
>

**2023年1月2日**

> 相关功能优化，修复重复上屏问题。
>

**2022年11月17日**

> 优化候选框向上颠倒时不超过屏幕上边缘。
>

**2022年11月16日**

> 候选项增加到26个，详情见设置界面。
>

**2022年10月23日**

> 优化截图上屏功能，截图上屏速度基本上跟得上打字速度了。
>

**2022年10月10日**

> 优化重复键引导标点功能，按重复快捷键后可以继续按编码串输入。
>

**2022年10月9日**

> 打字统计优化，取消单击上屏统计，删除不必要的统计等。
>

**2022年10月4日**

> 性能优化。
>

**2022年10月2日**

> 优化反应速度和屏幕刷新。
>

**2022年9月28日**

> 性能优化、修复；增加万能码元。
>

**2022年9月27日**

> 优化GDI绘制时的锯齿问题。
>

**2022年9月26日**

> 候选框增加调整行距功能。
>

**2022年9月25日**

> 添加大小写转换提示；数字统计界面添加码长统计。
>

**2022年9月22日**

> 优化字体设置，添加GDI+绘制
>

**2022年9月16日**

> 添加历史十重双检索功能。
>

**2022年9月5日**

> 优化上屏后取光标坐标组合片断之虚拟键发送。
>

**2022年9月3日**

> 添加反查显示开关及提示功能。
>

**2022年8月21日**

> 添加自动造词功能。
>

**2022年8月20日**

> 添加双检索功能，具体使用方法见软件设置界面和上文说明文档。
>

**2022年8月12日**

> 优化数字统计界面显示；
>
> 优化同一快捷键充当选重键和引导键问题（例如分号键为引导键和选重键，当敲击分号键后，直接输入编码时，分号起引导码表作用，当输入完编号后，再次按分号键时，则起选重作用）。
>

**2022年8月7日**

> 优化数字统计界面对齐问题；
>
> 修复码长顶屏问题。
>

**2022年7月23日**

> 添加直接导入txt码表功能。
>

**2022年7月18日**

> 优化过渡态时按数字键1直接上屏1。
>

**2022年7月16日**

> 增加左右Shift和左右Ctrl选重键，详细请看配置界面中的“上屏一般设置”中的《候选快键字符串》项。
>

**2022年7月12日**

> 增加删除码表快捷（Ctr+Del）。
>

**2022年6月21日**

> 改进句号转点功能。数字后句号自动变点，如果回删，则点号会自动变为句号;关联中文标点情况下，汉字后为句号，如果回删一次再次输入句号，自动变为点号。回删操作使得句号和点号不断转化。
>

**2022年6月18日**

> 添加数字后句号（2<font style="color:rgb(38, 38, 38);">。3）</font>自动变为点号功能（<font style="color:rgb(38, 38, 38);">2.3</font>）。
>

## 文档更新日志
本文档由毛博士于xx年xx月起，持续撰写并更新，惠及众多用户。

2025.5起，xbeta参与部分内容更新。<font style="color:#AE146E;">（2025-05-22 起，修订文字以紫色标示）</font>

最后更新日期：2025-05-22

  


---

---

[1]: https://zhuanlan.zhihu.com/p/291029476



> 更新: 2025-05-22 12:24:11  
> 原文: <https://www.yuque.com/gongzixiaobai-ypdqo/tnrbgm/yfqze3>