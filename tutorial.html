<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>使用教程 - 小科狗输入法</title>
    <meta name="description" content="小科狗输入法详细使用教程，包括安装、配置、使用技巧等完整指南。">
    <meta name="keywords" content="小科狗输入法,使用教程,安装指南,配置说明,KEG输入法">
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo.png" alt="小科狗输入法" class="logo-img">
                <span class="logo-text">小科狗输入法</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">首页</a></li>
                <li><a href="index.html#features">特色功能</a></li>
                <li><a href="index.html#download">下载</a></li>
                <li><a href="tutorial.html" class="active">教程</a></li>
                <li><a href="changelog.html">更新日志</a></li>
                <li><a href="index.html#community">社区</a></li>
                <li><a href="index.html#about">关于</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 教程页面头部 -->
    <section class="tutorial-hero">
        <div class="container">
            <h1>小科狗输入法使用教程</h1>
            <p class="tutorial-subtitle">从安装到精通，全面掌握小科狗输入法的使用方法</p>
            <nav class="tutorial-nav">
                <a href="#install" class="tutorial-nav-link">快速入门</a>
                <a href="#config" class="tutorial-nav-link">KEG配置</a>
                <a href="#basic" class="tutorial-nav-link">基础使用</a>
                <a href="#advanced" class="tutorial-nav-link">高级功能</a>
                <a href="#tips" class="tutorial-nav-link">使用技巧</a>
                <a href="#troubleshooting" class="tutorial-nav-link">问题解决</a>
            </nav>
        </div>
    </section>

    <!-- 安装指南 -->
    <section id="install" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">快速入门</h2>
            <div class="tutorial-content">
                <div class="install-intro">
                    <h3>小科狗输入法平台简介</h3>
                    <p>小科狗输入法平台，又名为 KEG 输入法（取 Keep Earth Green 首字母组合），是一款基于 C++，专为Windows 系统打造，融合了小狼毫、影子、大科狗等输入法平台特点的纯TSF架构的输入法平台。</p>
                    <p>小科狗与其他输入法有本质的不同，其目标是打造一款挂码方便、码字爽快和打遍天下所有编码方案的输入法平台。</p>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>下载、解压并安装</h3>
                        <p>由于小科狗目前无官方网站，如要下载安装小科狗输入法平台，选择以下两种方式之一下载：</p>
                        <ul class="step-list">
                            <li><strong>小科狗官方QQ群（641858743）：</strong>群文件 → "小科狗输入法"文件夹，下载"小科狗.7z"</li>
                            <li><strong>永硕E盘：</strong>"小科狗输入法"文件夹，下载最新版本安装包（请根据后面显示的日期下载最新版）</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/download-source.png" alt="下载来源">
                        </div>
                        <div class="step-note">
                            <strong>重要提示：</strong>小科狗的「安装」过程有些特别。普通软件运行下载的安装程序，点击「下一步」就能傻瓜式完成安装。但小科狗在某些方面像绿色软件，要把 7z 压缩包的内容手动解压出来；进行必要的手动权限准备；最后运行其中的安装引导程序。
                        </div>
                    </div>
                </div>
                <div class="tutorial-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>手动解压与权限设置</h3>
                        <p><strong>①手动解压：</strong>建议把 小科狗.7z 的内容，全部解压到 c:\Program Files (x86)\小科狗 目录。</p>
                        <p><strong>②手动权限、文件准备：</strong></p>
                        <ul class="step-list">
                            <li>对 KegServer.exe，以及 SiKegSetup-32bit.exe / SiKegSetup-64bit.exe ，设定管理员权限。具体方法是：选中文件，右击打开属性，打开"兼容性"，勾选"以管理员身份运行此程序"</li>
                            <li>如果安装在C盘，还建议把压缩包中的 Keg.db文件、Keg.txt文件以及car文件夹和zj文件夹复制到C:\SiKegInput文件夹里面，使之避开特殊文件夹的读写权限</li>
                            <li>如果你需要的不是现代五笔，而是86五笔等，也建议在此步骤替换db</li>
                        </ul>
                    </div>
                </div>
                <div class="tutorial-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>安装KEG输入法</h3>
                        <p>根据电脑操作系统的不同，双击 SiKegSetup-32bit.exe 或者 SiKegSetup-64bit.exe 运行安装程序，（稳妥起见，建议右键「管理员权限运行」）在弹出的对话框中选择点击"安装"按钮进行安装，最后提示小科狗安装成功。</p>
                        <div class="step-image">
                            <img src="images/tutorial/install-dialog.png" alt="安装对话框">
                        </div>
                    </div>
                </div>
                <div class="tutorial-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>启动KEG输入法</h3>
                        <p>安装完成后，需要启动KEG输入法服务：</p>
                        <ol class="step-list">
                            <li>双击运行 KegServer.exe（建议右键「管理员权限运行」）</li>
                            <li>如果一切正常，会在系统托盘（右下角）出现小科狗图标</li>
                            <li>按 <kbd>Ctrl</kbd>+<kbd>Shift</kbd> 切换到 KEG 输入法</li>
                            <li>如果能正常输入，说明安装成功</li>
                        </ol>
                        <div class="step-note">
                            <strong>重要提示：</strong>每次开机后都需要手动运行 KegServer.exe 来启动输入法服务。如需开机自启动，可将 KegServer.exe 添加到系统启动项。
                        </div>
                    </div>
                </div>
                <div class="tutorial-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>选择输入方案</h3>
                        <p>KEG输入法支持多种输入方案，首次使用时需要选择合适的方案：</p>
                        <ol class="step-list">
                            <li>右键点击系统托盘中的小科狗图标</li>
                            <li>选择"输入方案"菜单</li>
                            <li>从列表中选择您需要的输入方案（如现代五笔、拼音等）</li>
                            <li>选择后即可开始使用该方案进行输入</li>
                        </ol>
                        <div class="step-image">
                            <img src="images/tutorial/scheme-selection.png" alt="方案选择界面">
                        </div>
                        <div class="step-note">
                            <strong>提示：</strong>KEG默认提供现代五笔方案，如需其他方案（如86五笔、拼音等），可以下载对应的码表文件进行替换。
                        </div>
                    </div>
                </div>
                <div class="tutorial-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h3>常见安装问题</h3>
                        <ul class="step-list">
                            <li>如遇"无法注册输入法"或"未出现在输入法列表"，请以管理员身份重新安装。</li>
                            <li>如被安全软件拦截，请添加信任或临时关闭安全软件。</li>
                            <li>如遇系统提示"文件损坏"，请重新下载安装包。</li>
                        </ul>
                        <div class="step-note">
                            <strong>遇到问题？</strong>请加入QQ群或参考<a href="#troubleshooting">常见问题解决</a>部分。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 第二章：小科狗其它功能的配置 -->
    <section id="config" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">第二章：小科狗其它功能的配置</h2>
            <div class="tutorial-content">
                <div class="config-intro">
                    <h3>2.1 候选界面及状态栏设置</h3>
                    <p>小科狗输入法平台每个码表候选界面除了可通过小科狗配置助手内置配色方案进行选择快速设置外，还可利用小科狗内置的纯文本设置界面进行更加具体的设置。</p>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2.1.1</div>
                    <div class="step-content">
                        <h3>候选界面设置</h3>
                        <p>请按Ctrl+Shift切换至小科狗输入法，按任意按键呼出小科狗候选项界面。如果要对候选界面进行相关设置，在候选界面上右击，选择"设置当前编码方案配置"或者直接按Ctrl+Shift+空格，进入小科狗方案配置界面：</p>
                        <div class="step-image">
                            <img src="images/tutorial/candidate-window.png" alt="候选界面">
                        </div>
                        <div class="step-image">
                            <img src="images/tutorial/candidate-window-settings.png" alt="候选窗口设置">
                        </div>

                        <h4>候选窗口的设置主要包括以下几个方面：</h4>
                        <ul class="step-list">
                            <li><strong>候选个数：</strong>候选界面显示的候选项数量，其值为1-26，根据自己的需求自行设置</li>
                            <li><strong>候选的高度间距和宽度间距：</strong>设置候选框横向或者竖向各个候选项的间隔距离</li>
                            <li><strong>候选窗口横向、竖向设置：</strong>候选界面是横向还是竖向显示，默认为横向</li>
                            <li><strong>要显示背景图吗：</strong>默认为不要，如果改为"要"，则会显示背景图效果</li>
                            <li><strong>高度宽度要完全自动调整吗：</strong>默认为"要"，会根据候选界面字词的长短及候选项数量的变化自动调整长度和宽度</li>
                            <li><strong>窗口四个角要圆角吗：</strong>默认为"不要"，如果设置为"要"，则四个角会呈倒圆角</li>
                            <li><strong>候选窗口颜色的调整：</strong>可以设置边框色、选中色、字体色等各种颜色配置</li>
                        </ul>

                        <div class="step-note">
                            <strong>重要提示：</strong>在设置完成后，请选择"保存内存数据库到硬盘"，否则当前设置不会同步到对应码表中去。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2.1.2</div>
                    <div class="step-content">
                        <h3>状态栏配置</h3>
                        <p>小科狗状态栏目前处于试用阶段，状态栏显示可以通过鼠标左键单击手动到桌面任意位置放置，包括放置于任务栏上面。</p>
                        <p>状态栏由三部分构成：最前面的logo，中间的中英文提示（中文状态为黑色字体的"中c"，英文状态为红色字体的"英e"），最后面的码表名。</p>
                        <p>如果不想显示状态栏，则在候选框显示的情况下，默认按Ctrl+F1，即可将状态栏隐藏。</p>

                        <h4>状态栏皮肤配置：</h4>
                        <p>目前小科狗支持静态和动态皮肤配置，设置位置在安装包下的keg.txt文件中的"状态栏"一行。默认配置快捷键为左ctrl+小键盘点号键。</p>
                        <ul class="step-list">
                            <li><strong>提示文本的位置：</strong>状态栏中的文字可以设置在三个位置：上对齐、居中、下对齐</li>
                            <li><strong>提示文本要隐藏吗：</strong>选择要或不要来控制提示文字的显示</li>
                            <li><strong>提示文本字体色：</strong>设置提示文字的颜色</li>
                            <li><strong>提示文本字体大小：</strong>设置提示文字的大小</li>
                            <li><strong>提示文本字体名称：</strong>设置提示文字的字体</li>
                        </ul>

                        <div class="step-note">
                            <strong>注意：</strong>所使用的图片可以放在任意位置，并且任意名称都行。但动态图要是真正的gif图片。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2.2</div>
                    <div class="step-content">
                        <h3>其它输入习惯设置</h3>
                        <p>本设置基于86版五笔个人使用习惯加以说明，不同输入法方案以及个人使用习惯的不同，可能设置有所差异，请依据本说明，酌情进行相关设置。</p>

                        <h4>2.2.1 动作设置</h4>
                        <p>动作设置项中包括10项设置，每项具体如下：</p>
                        <ul class="step-list">
                            <li><strong>要显示键首字根吗?=不要：</strong>该项设置主要针对键字根码表，如果value值是以键首字根表示，则建议开启</li>
                            <li><strong>上屏后候选窗口要立即消失吗?=要：</strong>建议设置为要</li>
                            <li><strong>超过码长要清屏吗?=要：</strong>建议设置为要</li>
                            <li><strong>无候选要清屏吗?=要：</strong>建议设置为要</li>
                            <li><strong>要启用最大码长无候选清屏吗?=要：</strong>建议设置为要</li>
                            <li><strong>无候选敲空格要上屏编码串吗?=要：</strong>请自行试验该功能是否要开启</li>
                            <li><strong>Shift键上屏编码串吗?=要：</strong>要则在不上屏候选项的情况下，按shift直接上屏编码串</li>
                            <li><strong>Enter键上屏编码串吗?=要：</strong>要则效果同上</li>
                            <li><strong>Enter键上屏并使首个字母大写吗?=要：</strong>要是则直接上屏编码串，并且首个字母大写</li>
                            <li><strong>Backspace键一次性删除前次上屏的内容吗?=不要：</strong>如果为要，则Backspace键一次删除上次输入内容，不要是逐字删除</li>
                        </ul>

                        <h4>2.2.2 重复上屏、码表调频与检索设置</h4>
                        <ul class="step-list">
                            <li><strong>最大码长：</strong>码表设置中的最大码长默认为5，如果为86五笔，最大码长本身为4，则将此处的5改为4</li>
                            <li><strong>重复上屏码元字符串：</strong>即为重复上屏快捷键设置，可以根据个人使用习惯设置重复上屏快捷键</li>
                            <li><strong>要逐码提示检索吗?=要：</strong>默认为要，则候选界面不但会显示编码串完全匹配的候选项，也会显示包含输入编码串的候选项</li>
                            <li><strong>要显示逐码提示吗?=要：</strong>默认要，则会显示编码串提示</li>
                            <li><strong>要显示反查提示吗?=要：</strong>反查提示设置，默认要，如果将"要"改为"不要"，则不会提示反查内容</li>
                            <li><strong>要启用上屏自动增加调频权重吗?=要：</strong>默认为要，即某一字、词输出一次，则其码表中的权重值（weight）加1</li>
                            <li><strong>要启用上屏自动增加调频权重直接到顶吗?=要：</strong>此功能开启时，输入某一字词，则该字词会自动调频到第一候选项</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/repeat-input.png" alt="重复上屏功能">
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2.3</div>
                    <div class="step-content">
                        <h3>码表的导入、导出及删除</h3>

                        <h4>2.3.1 码表的导入</h4>
                        <p><strong>1）码表的直接导入</strong></p>
                        <p>小科狗支持txt格式码表的直接导入，请先新建txt文档，第一行录入码表头文件。呼出候选框，鼠标在候选框上面右击选择加载txt码表到内存数据，打开码表加载界面，点击"设置码表路径"按钮，加载设置好的txt码表，点击下方的应用或确定，即可将码表导入进码表数据库中。</p>

                        <p><strong>2）通过数据库软件导入</strong></p>
                        <p>小科狗自带超过20种输入法码表，利用DB Browser for SQLite数据库管理软件打开小科狗安装文件夹下的keg.db码表数据库文件。小科狗输入法平台可以导入任何输入法码表，但是必须保证码表有四个字段：key（编码列）、value（词条列）、weight（权重列）和fc（反查列）字段，否则输入法平台无法识别码表格式。</p>

                        <h4>2.3.2 码表的导出</h4>
                        <p>小科狗输入法支持码表的直接导出，在候选框或者状态条上右击呼出菜单栏，选择"导出txt码表到指定文件夹"，呼出码表导出对话框。码表导出界面由两部分组成，上面为码表导出文件夹选择（目标文件夹），下面为设置所要导出的码表（目标码表名）。</p>

                        <h4>2.3.3 码表的删除</h4>
                        <p><strong>1）码表的直接删除</strong></p>
                        <p>按快捷键呼出码表菜单，利用上下箭头移到到需要删除的码表上面，按Ctrl+DEL快捷键即可将选中的码表删除。</p>

                        <p><strong>2）通过数据库软件删除</strong></p>
                        <p>如果不想要数据库中的相关码表，想将其删除，则在DB Browser for SQLite软件中，选中对应码表，右击删除表即可，然后在小科狗中呼出候选框界面，再次"保存内存数据库到硬盘"即可。</p>

                        <h4>2.3.4 大词库功能</h4>
                        <p>小科狗支持百万级大词库功能，实现方式为将码表命名为以"大词库"开头即可。例如原有码表名为"86五笔"，要想实现百万级大词库支持功能，将码表命名为"大词库86五笔"即可。</p>
                        <div class="step-note">
                            <strong>注意：</strong>百万级大词库功能会影响到调频等功能，望周知。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 基础使用 -->
    <section id="basic" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">基础使用</h2>

            <div class="tutorial-content">
                <div class="basic-intro">
                    <h3>开始使用KEG输入法</h3>
                    <p>KEG输入法安装完成后，您就可以开始使用了。KEG支持多种输入方案，包括拼音、五笔、双拼等，可以满足不同用户的需求。</p>
                </div>

                <div class="tutorial-card">
                    <h3>🔄 切换输入法</h3>
                    <p>使用快捷键快速切换到小科狗输入法：</p>
                    <div class="shortcut-box">
                        <kbd>Ctrl</kbd> + <kbd>Shift</kbd> 切换输入法
                    </div>
                    <div class="shortcut-box">
                        <kbd>Ctrl</kbd> + <kbd>Space</kbd> 中英文切换
                    </div>
                    <div class="shortcut-box">
                        <kbd>Shift</kbd> + <kbd>Space</kbd> 全角半角切换
                    </div>
                    <p><strong>提示：</strong>首次使用时，请确保在任务栏右下角能看到KEG输入法的图标。如果没有，请检查输入法是否正确安装。</p>
                </div>

                <div class="tutorial-card">
                    <h3>⌨️ 基本输入操作</h3>
                    <p>KEG输入法支持多种输入方案，可以根据需要选择：</p>
                    <ul>
                        <li><strong>拼音输入：</strong>支持全拼和简拼输入</li>
                        <li><strong>五笔输入：</strong>支持五笔86和五笔98</li>
                        <li><strong>双拼输入：</strong>支持多种双拼方案</li>
                        <li><strong>方案切换：</strong>可以快速在不同方案间切换</li>
                    </ul>
                    <div class="input-example">
                        <h4>输入示例：</h4>
                        <div class="example-item">
                            <span class="input-code">拼音：shurufa</span>
                            <span class="arrow">→</span>
                            <span class="output-text">输入法</span>
                        </div>
                        <div class="example-item">
                            <span class="input-code">五笔：srf</span>
                            <span class="arrow">→</span>
                            <span class="output-text">输入法</span>
                        </div>
                    </div>
                </div>

                <div class="tutorial-card">
                    <h3>🎯 候选词选择</h3>
                    <p>KEG提供多种候选词选择方式，提高输入效率：</p>
                    <div class="candidate-demo">
                        <img src="images/candidate-selection.png" alt="候选词选择示例">
                    </div>
                    <div class="selection-methods">
                        <h4>选择方法：</h4>
                        <ul>
                            <li><strong>数字键 1-9：</strong>选择对应位置的候选词</li>
                            <li><strong>空格键：</strong>选择第一个候选词（最常用）</li>
                            <li><strong>Tab键：</strong>选择第二个候选词</li>
                            <li><strong>回车键：</strong>确认当前输入的编码</li>
                            <li><strong>方向键：</strong>上下翻页，左右移动光标</li>
                            <li><strong>Page Up/Down：</strong>快速翻页</li>
                        </ul>
                    </div>
                </div>

                <div class="tutorial-card">
                    <h3>📝 输入技巧</h3>
                    <div class="tips-grid-basic">
                        <div class="tip-item">
                            <h4>🚀 快速输入</h4>
                            <ul>
                                <li>使用简拼：输入声母快速匹配</li>
                                <li>词组输入：输入多个字的首字母</li>
                                <li>自动记忆：常用词会自动提升优先级</li>
                            </ul>
                        </div>
                        <div class="tip-item">
                            <h4>🔧 编辑功能</h4>
                            <ul>
                                <li>Backspace：删除前一个字符</li>
                                <li>Delete：删除后一个字符</li>
                                <li>Ctrl+Backspace：删除整个词组</li>
                            </ul>
                        </div>
                        <div class="tip-item">
                            <h4>📋 特殊输入</h4>
                            <ul>
                                <li>标点符号：直接输入或使用快捷键</li>
                                <li>数字输入：支持中文数字转换</li>
                                <li>日期时间：支持快速日期输入</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="tutorial-card">
                    <h3>⚙️ 状态栏功能</h3>
                    <p>KEG输入法状态栏提供了丰富的功能选项：</p>
                    <div class="statusbar-demo">
                        <img src="images/statusbar-demo.png" alt="状态栏功能">
                    </div>
                    <div class="statusbar-functions">
                        <h4>操作方式：</h4>
                        <ul>
                            <li><strong>左键点击：</strong>显示/隐藏候选词窗口</li>
                            <li><strong>右键点击：</strong>打开功能菜单</li>
                            <li><strong>双击：</strong>快速切换输入方案</li>
                            <li><strong>拖拽：</strong>移动状态栏位置</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 高级功能 -->
    <section id="advanced" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">高级功能</h2>

            <div class="tutorial-content">
                <div class="advanced-intro">
                    <h3>深度定制KEG输入法</h3>
                    <p>KEG输入法提供了丰富的高级功能，让您可以根据个人习惯深度定制输入体验。这些功能包括方案管理、词库定制、皮肤设置等。</p>
                </div>

                <div class="feature-item-large">
                    <h3>📚 输入方案管理</h3>
                    <p>KEG最大的特色是支持多种输入方案，可以满足不同用户的需求：</p>

                    <div class="scheme-details">
                        <h4>内置方案：</h4>
                        <div class="scheme-grid">
                            <div class="scheme-card">
                                <h5>🔤 现代五笔</h5>
                                <p>基于五笔字型的现代化改进版本，支持词组输入和智能联想。</p>
                                <div class="scheme-features">
                                    <span class="feature-tag">词组输入</span>
                                    <span class="feature-tag">智能联想</span>
                                    <span class="feature-tag">简码优化</span>
                                </div>
                            </div>
                            <div class="scheme-card">
                                <h5>🎯 拼音方案</h5>
                                <p>支持全拼、简拼、模糊音等多种拼音输入方式。</p>
                                <div class="scheme-features">
                                    <span class="feature-tag">全拼输入</span>
                                    <span class="feature-tag">简拼支持</span>
                                    <span class="feature-tag">模糊音</span>
                                </div>
                            </div>
                            <div class="scheme-card">
                                <h5>⚡ 双拼方案</h5>
                                <p>支持多种双拼方案，提高拼音输入效率。</p>
                                <div class="scheme-features">
                                    <span class="feature-tag">小鹤双拼</span>
                                    <span class="feature-tag">自然码</span>
                                    <span class="feature-tag">微软双拼</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="how-to-detailed">
                        <h4>方案切换方法：</h4>
                        <ol>
                            <li>右键点击KEG输入法状态栏</li>
                            <li>选择"输入方案"菜单</li>
                            <li>从列表中选择需要的方案</li>
                            <li>或使用快捷键Ctrl+`快速切换</li>
                        </ol>
                        <div class="scheme-image">
                            <img src="images/scheme-switch.png" alt="方案切换界面">
                        </div>
                    </div>
                </div>

                <div class="feature-item-large">
                    <h3>📖 词库管理系统</h3>
                    <p>KEG提供强大的词库管理功能，支持导入、编辑、备份词库：</p>

                    <div class="dict-management">
                        <h4>词库文件说明：</h4>
                        <ul>
                            <li><strong>Keg.db：</strong>主词库文件，包含所有输入方案的词汇数据</li>
                            <li><strong>用户词库：</strong>存储个人添加的词汇和使用习惯</li>
                            <li><strong>专业词库：</strong>可导入医学、法律、计算机等专业词库</li>
                        </ul>

                        <h4>词库操作：</h4>
                        <div class="dict-operations">
                            <div class="operation-item">
                                <h5>📥 导入词库</h5>
                                <ol>
                                    <li>准备词库文件（支持.txt、.dict等格式）</li>
                                    <li>右键状态栏 → 设置 → 词库管理</li>
                                    <li>点击"导入词库"按钮</li>
                                    <li>选择词库文件并确认导入</li>
                                </ol>
                            </div>
                            <div class="operation-item">
                                <h5>✏️ 编辑词库</h5>
                                <ol>
                                    <li>打开词库管理界面</li>
                                    <li>选择要编辑的词库</li>
                                    <li>添加、删除或修改词条</li>
                                    <li>保存更改并重新加载</li>
                                </ol>
                            </div>
                            <div class="operation-item">
                                <h5>💾 备份词库</h5>
                                <ol>
                                    <li>定期备份Keg.db文件</li>
                                    <li>导出个人词库设置</li>
                                    <li>保存到安全位置</li>
                                    <li>需要时可快速恢复</li>
                                </ol>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="feature-item-large">
                    <h3>🎨 皮肤与界面定制</h3>
                    <p>KEG支持静态和动态皮肤，让您的输入法界面更加个性化：</p>

                    <div class="skin-system">
                        <h4>皮肤类型：</h4>
                        <div class="skin-types">
                            <div class="skin-type">
                                <h5>🖼️ 静态皮肤</h5>
                                <p>使用固定的背景图片和颜色方案</p>
                                <ul>
                                    <li>支持PNG、JPG格式</li>
                                    <li>可自定义字体颜色</li>
                                    <li>支持透明效果</li>
                                </ul>
                            </div>
                            <div class="skin-type">
                                <h5>🎬 动态皮肤</h5>
                                <p>支持动画效果和交互反馈</p>
                                <ul>
                                    <li>动画背景效果</li>
                                    <li>按键反馈动画</li>
                                    <li>候选词高亮效果</li>
                                </ul>
                            </div>
                        </div>

                        <h4>皮肤配置：</h4>
                        <p>皮肤配置文件位于安装目录下的keg.txt文件中，您可以手动编辑或使用图形界面设置：</p>
                        <div class="config-example">
                            <h5>配置示例：</h5>
                            <pre><code>[Skin]
BackgroundImage=skin/default.png
FontColor=#000000
CandidateColor=#333333
HighlightColor=#0078d4
Transparency=80</code></pre>
                        </div>
                    </div>
                </div>

                <div class="feature-grid">
                    <div class="feature-item">
                        <h3>⚡ 基础功能</h3>
                        <ul>
                            <li><strong>词频调整：</strong>常用词会自动提升优先级</li>
                            <li><strong>候选词设置：</strong>可调整候选词显示数量</li>
                            <li><strong>快捷键配置：</strong>自定义各种快捷键</li>
                            <li><strong>编码提示：</strong>显示字词的编码信息</li>
                        </ul>
                    </div>

                    <div class="feature-item">
                        <h3>🔧 界面设置</h3>
                        <ul>
                            <li><strong>皮肤选择：</strong>多种界面皮肤可选</li>
                            <li><strong>字体设置：</strong>调整候选词字体大小</li>
                            <li><strong>窗口位置：</strong>自定义候选词窗口位置</li>
                            <li><strong>透明度：</strong>调整界面透明度效果</li>
                        </ul>
                    </div>

                    <div class="feature-item">
                        <h3>📝 输入设置</h3>
                        <ul>
                            <li><strong>标点处理：</strong>中英文标点符号设置</li>
                            <li><strong>翻页键：</strong>自定义翻页按键</li>
                            <li><strong>重码处理：</strong>重码时的选择方式</li>
                            <li><strong>造词功能：</strong>手动添加新词到词库</li>
                        </ul>
                    </div>

                    <div class="feature-item">
                        <h3>🔄 方案管理</h3>
                        <ul>
                            <li><strong>方案切换：</strong>在不同输入方案间切换</li>
                            <li><strong>方案配置：</strong>调整各方案的参数</li>
                            <li><strong>码表管理：</strong>导入和管理码表文件</li>
                            <li><strong>备份恢复：</strong>备份和恢复配置设置</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 使用技巧 -->
    <section id="tips" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">使用技巧</h2>
            
            <div class="tips-grid">
                <div class="tip-card">
                    <div class="tip-icon">💡</div>
                    <h3>快速输入技巧</h3>
                    <ul>
                        <li>熟练掌握所选输入方案的编码规则</li>
                        <li>使用简码：常用字词通常有简短编码</li>
                        <li>词组输入：学会输入常用词组提高效率</li>
                        <li>快捷键：熟练使用各种快捷键操作</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">⌨️</div>
                    <h3>快捷键大全</h3>
                    <ul>
                        <li><strong><kbd>Ctrl</kbd>+<kbd>Shift</kbd>：</strong>切换输入法</li>
                        <li><strong><kbd>Ctrl</kbd>+<kbd>Space</kbd>：</strong>中英文切换</li>
                        <li><strong><kbd>Shift</kbd>+<kbd>Space</kbd>：</strong>全角半角切换</li>
                        <li><strong><kbd>Ctrl</kbd>+<kbd>.</kbd>：</strong>中英文标点切换</li>
                    </ul>
                </div>

                <div class="tip-card">
                    <div class="tip-icon">🔧</div>
                    <h3>配置优化</h3>
                    <ul>
                        <li>根据使用习惯调整候选词数量</li>
                        <li>选择合适的皮肤和字体大小</li>
                        <li>配置常用的快捷键</li>
                        <li>定期备份配置文件</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- 问题解决 -->
    <section id="troubleshooting" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">常见问题解决</h2>
            
            <div class="troubleshooting-content">
                <div class="problem-item">
                    <h3>❌ 输入法无法切换</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>检查输入法是否正确安装</li>
                            <li>重启输入法服务：任务管理器 → 结束输入法进程 → 重新启动</li>
                            <li>检查快捷键设置是否冲突</li>
                            <li>以管理员身份重新安装</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>🐌 输入法响应缓慢</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>关闭不必要的后台程序</li>
                            <li>清理输入法缓存</li>
                            <li>减少候选词数量</li>
                            <li>关闭复杂的皮肤主题</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>📝 候选词不准确</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>更新词库到最新版本</li>
                            <li>手动添加常用词汇</li>
                            <li>调整词频统计</li>
                            <li>重置用户词库</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>🔧 配置文件问题</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>检查keg.txt配置文件格式是否正确</li>
                            <li>确保配置文件编码为UTF-8</li>
                            <li>备份后重置为默认配置</li>
                            <li>参考官方配置文档进行修改</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>💾 词库损坏</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>从备份恢复Keg.db文件</li>
                            <li>重新下载官方词库文件</li>
                            <li>使用词库修复工具（如有）</li>
                            <li>重新安装输入法</li>
                        </ol>
                    </div>
                </div>

                <div class="problem-item">
                    <h3>🎨 皮肤显示异常</h3>
                    <div class="solution">
                        <h4>解决方案：</h4>
                        <ol>
                            <li>检查皮肤文件是否完整</li>
                            <li>确认图片格式支持（PNG/JPG）</li>
                            <li>重置为默认皮肤</li>
                            <li>更新显卡驱动程序</li>
                        </ol>
                    </div>
                </div>

                <div class="troubleshooting-tips">
                    <h3>💡 获取帮助</h3>
                    <div class="tips-container">
                        <div class="troubleshooting-tip">
                            <h4>📞 联系方式</h4>
                            <p>如果遇到问题，可以通过以下方式获取帮助：</p>
                            <ul>
                                <li>加入官方QQ群：641858743、498060191、641389627</li>
                                <li>查看B站视频教程了解详细操作</li>
                                <li>访问语雀文档获取最新说明</li>
                                <li>在QQ群中反馈问题和建议</li>
                            </ul>
                        </div>

                        <div class="troubleshooting-tip">
                            <h4>🛠️ 基本排查</h4>
                            <p>遇到问题时的基本排查步骤：</p>
                            <ul>
                                <li>确认KEG是否正确安装并出现在输入法列表</li>
                                <li>检查配置文件是否完整</li>
                                <li>尝试重新启动输入法</li>
                                <li>如果问题严重，可以重新安装</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 更多教程资源 -->
    <section class="more-tutorials">
        <div class="container">
            <h2 class="section-title">更多教程细节查看</h2>
            <p class="section-subtitle">获取更详细的使用说明和视频演示</p>

            <div class="tutorial-resources">
                <div class="resource-card">
                    <div class="resource-icon">📖</div>
                    <h3>详细使用手册</h3>
                    <p>完整的KEG输入法使用说明文档，包含所有功能的详细介绍和配置方法。</p>
                    <div class="resource-features">
                        <span class="feature-tag">完整功能说明</span>
                        <span class="feature-tag">配置详解</span>
                        <span class="feature-tag">常见问题</span>
                    </div>
                    <a href="https://www.yuque.com/gongzixiaobai-ypdqo/tnrbgm/yfqze3" target="_blank" class="btn btn-primary">查看详细文档</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">🎥</div>
                    <h3>视频教程</h3>
                    <p>B站视频教程，直观演示KEG输入法的安装、配置和使用方法。</p>
                    <div class="resource-features">
                        <span class="feature-tag">安装演示</span>
                        <span class="feature-tag">功能介绍</span>
                        <span class="feature-tag">实操指导</span>
                    </div>
                    <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" class="btn btn-primary">观看视频教程</a>
                </div>

                <div class="resource-card">
                    <div class="resource-icon">💬</div>
                    <h3>社区支持</h3>
                    <p>加入官方QQ群，与其他用户交流使用经验，获取实时技术支持。</p>
                    <div class="resource-features">
                        <span class="feature-tag">实时答疑</span>
                        <span class="feature-tag">经验分享</span>
                        <span class="feature-tag">问题反馈</span>
                    </div>
                    <div class="qq-groups-inline">
                        <span class="qq-group-inline" title="点击复制群号">641858743</span>
                        <span class="qq-group-inline" title="点击复制群号">498060191</span>
                        <span class="qq-group-inline" title="点击复制群号">641389627</span>
                    </div>
                    <div class="step-image">
                        <img src="images/tutorial/qq-group-qrcode.png" alt="QQ群二维码">
                    </div>
                </div>
            </div>

            <div class="tutorial-tips">
                <h3>💡 学习建议</h3>
                <div class="tips-container-inline">
                    <div class="tip-inline">
                        <strong>1. 先看文档：</strong>阅读详细使用手册，了解基本概念和功能
                    </div>
                    <div class="tip-inline">
                        <strong>2. 观看视频：</strong>通过视频教程直观学习操作方法
                    </div>
                    <div class="tip-inline">
                        <strong>3. 实践操作：</strong>按照教程步骤实际操作，加深理解
                    </div>
                    <div class="tip-inline">
                        <strong>4. 加入社群：</strong>遇到问题及时在QQ群中寻求帮助
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>小科狗输入法</h4>
                    <p>永远免费的输入法平台</p>
                    <div class="footer-social">
                        <a href="#" title="QQ群">💬</a>
                        <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" title="B站">📺</a>
                        <a href="https://www.toutiao.com/c/user/token/MS4wLjABAAAAsItj-RKKszxsyp_4Y_D82QpBTYN4X13J_rt3giogb1B1ZEiJDQZTJAQLw_ZJv1SP" target="_blank" title="头条">📰</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="index.html#download">下载软件</a></li>
                        <li><a href="tutorial.html">使用手册</a></li>
                        <li><a href="index.html#tutorial">视频教程</a></li>
                        <li><a href="index.html#community">加入社区</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <ul>
                        <li>QQ群1: 641858743</li>
                        <li>QQ群2: 498060191</li>
                        <li>QQ群3: 641389627</li>
                        <li>网站: xiaokegou.com</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>支持我们</h4>
                    <p>如果您觉得小科狗输入法好用，欢迎打赏支持开发者继续改进产品。</p>
                    <button class="btn btn-donate">❤️ 打赏支持</button>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 小科狗输入法. 保留所有权利. | 网站: xiaokegou.com</p>
                <p>Keep Earth Green - 让输入更美好</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
