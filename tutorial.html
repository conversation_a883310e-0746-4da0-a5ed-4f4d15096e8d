<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小科狗输入法使用教程 - 从入门到精通</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo.png" alt="小科狗输入法" class="logo-img">
                <span class="logo-text">小科狗输入法</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">首页</a></li>
                <li><a href="index.html#features">特色功能</a></li>
                <li><a href="index.html#download">下载</a></li>
                <li><a href="tutorial.html" class="active">教程</a></li>
                <li><a href="changelog.html">更新日志</a></li>
                <li><a href="index.html#community">社区</a></li>
                <li><a href="index.html#about">关于</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 教程标题区 -->
    <section class="tutorial-hero">
        <div class="container">
            <h1>小科狗输入法使用教程</h1>
            <p class="tutorial-subtitle">从安装到精通，全面掌握小科狗输入法的使用方法</p>
            <nav class="tutorial-nav">
                <a href="#quickstart" class="tutorial-nav-link">快速入门</a>
                <a href="#config-usage" class="tutorial-nav-link">配置和使用</a>
                <a href="#advanced" class="tutorial-nav-link">高级功能</a>
                <a href="#troubleshooting" class="tutorial-nav-link">问题解决</a>
            </nav>
        </div>
    </section>

    <!-- 快速入门 -->
    <section id="quickstart" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">快速入门</h2>
            <div class="tutorial-content">
                <div class="intro-box">
                    <h3>小科狗输入法平台简介</h3>
                    <p>小科狗输入法平台，又名为 KEG 输入法（取 Keep Earth Green 首字母组合），是一款基于 C++，专为Windows 系统打造，融合了小狼毫、影子、大科狗等输入法平台特点的纯TSF架构的输入法平台。</p>
                    <p>小科狗与其他输入法有本质的不同，其目标是打造一款挂码方便、码字爽快和打遍天下所有编码方案的输入法平台。</p>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>下载、解压并安装</h3>
                        <p>由于小科狗目前无官方网站，如要下载安装小科狗输入法平台，选择以下两种方式之一下载：</p>
                        <ul class="step-list">
                            <li><strong>小科狗官方QQ群（641858743）：</strong>群文件 → "小科狗输入法"文件夹，下载"小科狗.7z"</li>
                            <li><strong>永硕E盘：</strong>"小科狗输入法"文件夹，下载最新版本安装包（请根据后面显示的日期下载最新版）</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/download-source.png" alt="下载来源">
                        </div>
                        <div class="step-note">
                            <strong>重要提示：</strong>小科狗的「安装」过程有些特别。普通软件运行下载的安装程序，点击「下一步」就能傻瓜式完成安装。但小科狗在某些方面像绿色软件，要把 7z 压缩包的内容手动解压出来；进行必要的手动权限准备；最后运行其中的安装引导程序。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>手动解压与权限设置</h3>
                        <p><strong>①手动解压：</strong>建议把 小科狗.7z 的内容，全部解压到 c:\Program Files (x86)\小科狗 目录。</p>
                        <p><strong>②手动权限、文件准备：</strong></p>
                        <ul class="step-list">
                            <li>对 KegServer.exe，以及 SiKegSetup-32bit.exe / SiKegSetup-64bit.exe ，设定管理员权限。具体方法是：选中文件，右击打开属性，打开"兼容性"，勾选"以管理员身份运行此程序"</li>
                            <li>如果安装在C盘，还建议把压缩包中的 Keg.db文件、Keg.txt文件以及car文件夹和zj文件夹复制到C:\SiKegInput文件夹里面，使之避开特殊文件夹的读写权限</li>
                            <li>如果你需要的不是现代五笔，而是86五笔等，也建议在此步骤替换db</li>
                        </ul>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>安装KEG输入法</h3>
                        <p>根据电脑操作系统的不同，双击 SiKegSetup-32bit.exe 或者 SiKegSetup-64bit.exe 运行安装程序，（稳妥起见，建议右键「管理员权限运行」）在弹出的对话框中选择点击"安装"按钮进行安装，最后提示小科狗安装成功。</p>
                        <div class="step-image">
                            <img src="images/tutorial/install-dialog.png" alt="安装对话框">
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>启动KEG输入法</h3>
                        <p>安装完成后，需要启动KEG输入法服务：</p>
                        <ol class="step-list">
                            <li>双击运行 KegServer.exe（建议右键「管理员权限运行」）</li>
                            <li>如果一切正常，会在系统托盘（右下角）出现小科狗图标</li>
                            <li>按 <kbd>Ctrl</kbd>+<kbd>Shift</kbd> 切换到 KEG 输入法</li>
                            <li>如果能正常输入，说明安装成功</li>
                        </ol>
                        <div class="step-note">
                            <strong>重要提示：</strong>每次开机后都需要手动运行 KegServer.exe 来启动输入法服务。如需开机自启动，可将 KegServer.exe 添加到系统启动项。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>选择输入方案</h3>
                        <p>KEG输入法支持多种输入方案，首次使用时需要选择合适的方案：</p>
                        <ol class="step-list">
                            <li>右键点击系统托盘中的小科狗图标</li>
                            <li>选择"输入方案"菜单</li>
                            <li>从列表中选择您需要的输入方案（如现代五笔、拼音等）</li>
                            <li>选择后即可开始使用该方案进行输入</li>
                        </ol>
                        <div class="step-image">
                            <img src="images/tutorial/scheme-selection.png" alt="方案选择界面">
                        </div>
                        <div class="step-note">
                            <strong>提示：</strong>KEG默认提供现代五笔方案，如需其他方案（如86五笔、拼音等），可以下载对应的码表文件进行替换。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 配置和使用 -->
    <section id="config-usage" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">配置和使用</h2>
            <div class="tutorial-content">
                <div class="intro-box">
                    <h3>基本使用方法</h3>
                    <p>安装完成后，您就可以开始使用小科狗输入法了。本节将详细介绍如何配置和使用各种功能。</p>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>输入法切换</h3>
                        <p>使用快捷键快速切换到小科狗输入法：</p>
                        <ul class="step-list">
                            <li>按 <kbd>Ctrl</kbd>+<kbd>Shift</kbd> 切换输入法</li>
                            <li>按 <kbd>Ctrl</kbd>+<kbd>Space</kbd> 中英文切换</li>
                            <li>按 <kbd>Shift</kbd>+<kbd>Space</kbd> 全角半角切换</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/input-method-switch.png" alt="输入法切换示意">
                        </div>
                        <div class="step-note">
                            <strong>提示：</strong>首次使用时，请确保在任务栏右下角能看到KEG输入法的图标。如果没有，请检查输入法是否正确安装。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>基本输入操作</h3>
                        <p>KEG输入法的基本操作方式：</p>
                        <ul class="step-list">
                            <li><strong>拼音输入：</strong>直接输入拼音字母，系统会自动显示候选词</li>
                            <li><strong>五笔输入：</strong>输入五笔编码，支持词组和单字输入</li>
                            <li><strong>混合输入：</strong>可以在拼音和五笔之间快速切换</li>
                            <li><strong>英文输入：</strong>按Ctrl+Space切换到英文模式</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/candidate-window.png" alt="候选窗口">
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>候选词选择</h3>
                        <p>KEG提供多种候选词选择方式，提高输入效率：</p>
                        <ul class="step-list">
                            <li><strong>数字键 1-9：</strong>选择对应位置的候选词</li>
                            <li><strong>空格键：</strong>选择第一个候选词（最常用）</li>
                            <li><strong>Tab键：</strong>选择第二个候选词</li>
                            <li><strong>回车键：</strong>确认当前输入的编码</li>
                            <li><strong>方向键：</strong>上下翻页，左右移动光标</li>
                            <li><strong>Page Up/Down：</strong>快速翻页</li>
                        </ul>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>候选界面配置</h3>
                        <p>小科狗输入法平台每个码表候选界面除了可通过小科狗配置助手内置配色方案进行选择快速设置外，还可利用小科狗内置的纯文本设置界面进行更加具体的设置。</p>
                        <p>请按Ctrl+Shift切换至小科狗输入法，按任意按键呼出小科狗候选项界面。如果要对候选界面进行相关设置，在候选界面上右击，选择"设置当前编码方案配置"或者直接按Ctrl+Shift+空格，进入小科狗方案配置界面：</p>
                        <div class="step-image">
                            <img src="images/tutorial/candidate-window-settings.png" alt="候选窗口设置">
                        </div>

                        <h4>候选窗口的设置主要包括以下几个方面：</h4>
                        <ul class="step-list">
                            <li><strong>候选个数：</strong>候选界面显示的候选项数量，其值为1-26，根据自己的需求自行设置</li>
                            <li><strong>候选的高度间距和宽度间距：</strong>设置候选框横向或者竖向各个候选项的间隔距离</li>
                            <li><strong>候选窗口横向、竖向设置：</strong>候选界面是横向还是竖向显示，默认为横向</li>
                            <li><strong>要显示背景图吗：</strong>默认为不要，如果改为"要"，则会显示背景图效果</li>
                            <li><strong>高度宽度要完全自动调整吗：</strong>默认为"要"，会根据候选界面字词的长短及候选项数量的变化自动调整长度和宽度</li>
                            <li><strong>窗口四个角要圆角吗：</strong>默认为"不要"，如果设置为"要"，则四个角会呈倒圆角</li>
                            <li><strong>候选窗口颜色的调整：</strong>可以设置边框色、选中色、字体色等各种颜色配置</li>
                        </ul>

                        <div class="step-note">
                            <strong>重要提示：</strong>在设置完成后，请选择"保存内存数据库到硬盘"，否则当前设置不会同步到对应码表中去。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>状态栏配置</h3>
                        <p>小科狗状态栏目前处于试用阶段，状态栏显示可以通过鼠标左键单击手动到桌面任意位置放置，包括放置于任务栏上面。</p>
                        <p>状态栏由三部分构成：最前面的logo，中间的中英文提示（中文状态为黑色字体的"中c"，英文状态为红色字体的"英e"），最后面的码表名。</p>
                        <p>如果不想显示状态栏，则在候选框显示的情况下，默认按Ctrl+F1，即可将状态栏隐藏。</p>

                        <h4>操作方式：</h4>
                        <ul class="step-list">
                            <li><strong>左键点击：</strong>显示/隐藏候选词窗口</li>
                            <li><strong>右键点击：</strong>打开功能菜单</li>
                            <li><strong>双击：</strong>快速切换输入方案</li>
                            <li><strong>拖拽：</strong>移动状态栏位置</li>
                        </ul>

                        <h4>状态栏皮肤配置：</h4>
                        <p>目前小科狗支持静态和动态皮肤配置，设置位置在安装包下的keg.txt文件中的"状态栏"一行。默认配置快捷键为左ctrl+小键盘点号键。</p>
                        <ul class="step-list">
                            <li><strong>提示文本的位置：</strong>状态栏中的文字可以设置在三个位置：上对齐、居中、下对齐</li>
                            <li><strong>提示文本要隐藏吗：</strong>选择要或不要来控制提示文字的显示</li>
                            <li><strong>提示文本字体色：</strong>设置提示文字的颜色</li>
                            <li><strong>提示文本字体大小：</strong>设置提示文字的大小</li>
                            <li><strong>提示文本字体名称：</strong>设置提示文字的字体</li>
                        </ul>

                        <div class="step-note">
                            <strong>注意：</strong>所使用的图片可以放在任意位置，并且任意名称都行。但动态图要是真正的gif图片。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">6</div>
                    <div class="step-content">
                        <h3>KEG配置文件详解</h3>
                        <p>KEG输入法的配置主要通过 keg.txt 文件进行。该文件包含了输入法的各种设置，包括界面、行为、快捷键等。</p>
                        <p>keg.txt 配置文件通常位于以下位置：</p>
                        <ul class="step-list">
                            <li><strong>安装目录：</strong>C:\Program Files (x86)\小科狗\keg.txt</li>
                            <li><strong>用户目录：</strong>C:\SiKegInput\keg.txt（推荐）</li>
                            <li><strong>便携模式：</strong>与 KegServer.exe 同目录下的 keg.txt</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/keg-txt-config.png" alt="KEG配置文件">
                        </div>
                        <div class="step-note">
                            <strong>重要：</strong>修改配置文件后，需要重启 KegServer.exe 才能生效。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 高级功能 -->
    <section id="advanced" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">高级功能</h2>
            <div class="tutorial-content">
                <div class="intro-box">
                    <h3>小科狗的强大功能</h3>
                    <p>小科狗输入法不仅仅是一个普通的输入法，它还提供了许多强大的高级功能，让您的输入体验更加高效和便捷。</p>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h3>输入习惯设置</h3>
                        <p>小科狗提供丰富的输入习惯设置，让您可以根据个人喜好定制输入体验。</p>

                        <h4>动作设置</h4>
                        <p>动作设置项中包括10项设置，每项具体如下：</p>
                        <ul class="step-list">
                            <li><strong>要显示键首字根吗?=不要：</strong>该项设置主要针对键字根码表，如果value值是以键首字根表示，则建议开启</li>
                            <li><strong>上屏后候选窗口要立即消失吗?=要：</strong>建议设置为要</li>
                            <li><strong>超过码长要清屏吗?=要：</strong>建议设置为要</li>
                            <li><strong>无候选要清屏吗?=要：</strong>建议设置为要</li>
                            <li><strong>要启用最大码长无候选清屏吗?=要：</strong>建议设置为要</li>
                            <li><strong>无候选敲空格要上屏编码串吗?=要：</strong>请自行试验该功能是否要开启</li>
                            <li><strong>Shift键上屏编码串吗?=要：</strong>要则在不上屏候选项的情况下，按shift直接上屏编码串</li>
                            <li><strong>Enter键上屏编码串吗?=要：</strong>要则效果同上</li>
                            <li><strong>Enter键上屏并使首个字母大写吗?=要：</strong>要是则直接上屏编码串，并且首个字母大写</li>
                            <li><strong>Backspace键一次性删除前次上屏的内容吗?=不要：</strong>如果为要，则Backspace键一次删除上次输入内容，不要是逐字删除</li>
                        </ul>

                        <h4>重复上屏、码表调频与检索设置</h4>
                        <ul class="step-list">
                            <li><strong>最大码长：</strong>码表设置中的最大码长默认为5，如果为86五笔，最大码长本身为4，则将此处的5改为4</li>
                            <li><strong>重复上屏码元字符串：</strong>即为重复上屏快捷键设置，可以根据个人使用习惯设置重复上屏快捷键</li>
                            <li><strong>要逐码提示检索吗?=要：</strong>默认为要，则候选界面不但会显示编码串完全匹配的候选项，也会显示包含输入编码串的候选项</li>
                            <li><strong>要显示逐码提示吗?=要：</strong>默认要，则会显示编码串提示</li>
                            <li><strong>要显示反查提示吗?=要：</strong>反查提示设置，默认要，如果将"要"改为"不要"，则不会提示反查内容</li>
                            <li><strong>要启用上屏自动增加调频权重吗?=要：</strong>默认为要，即某一字、词输出一次，则其码表中的权重值（weight）加1</li>
                            <li><strong>要启用上屏自动增加调频权重直接到顶吗?=要：</strong>此功能开启时，输入某一字词，则该字词会自动调频到第一候选项</li>
                        </ul>
                        <div class="step-image">
                            <img src="images/tutorial/repeat-input.png" alt="重复上屏功能">
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h3>码表管理</h3>
                        <p>KEG提供强大的词库管理功能，支持导入、编辑、备份词库。</p>

                        <h4>词库文件说明：</h4>
                        <ul class="step-list">
                            <li><strong>Keg.db：</strong>主词库文件，包含所有输入方案的词汇数据</li>
                            <li><strong>用户词库：</strong>存储个人添加的词汇和使用习惯</li>
                            <li><strong>专业词库：</strong>可导入医学、法律、计算机等专业词库</li>
                        </ul>

                        <h4>码表的导入</h4>
                        <p><strong>码表的直接导入：</strong></p>
                        <p>小科狗支持txt格式码表的直接导入，请先新建txt文档，第一行录入码表头文件。呼出候选框，鼠标在候选框上面右击选择加载txt码表到内存数据，打开码表加载界面，点击"设置码表路径"按钮，加载设置好的txt码表，点击下方的应用或确定，即可将码表导入进码表数据库中。</p>

                        <p><strong>通过数据库软件导入：</strong></p>
                        <p>小科狗自带超过20种输入法码表，利用DB Browser for SQLite数据库管理软件打开小科狗安装文件夹下的keg.db码表数据库文件。小科狗输入法平台可以导入任何输入法码表，但是必须保证码表有四个字段：key（编码列）、value（词条列）、weight（权重列）和fc（反查列）字段，否则输入法平台无法识别码表格式。</p>

                        <h4>码表的导出</h4>
                        <p>小科狗输入法支持码表的直接导出，在候选框或者状态条上右击呼出菜单栏，选择"导出txt码表到指定文件夹"，呼出码表导出对话框。码表导出界面由两部分组成，上面为码表导出文件夹选择（目标文件夹），下面为设置所要导出的码表（目标码表名）。</p>

                        <h4>码表的删除</h4>
                        <p><strong>1）码表的直接删除：</strong></p>
                        <p>按快捷键呼出码表菜单，利用上下箭头移到到需要删除的码表上面，按Ctrl+DEL快捷键即可将选中的码表删除。</p>

                        <p><strong>2）通过数据库软件删除：</strong></p>
                        <p>如果不想要数据库中的相关码表，想将其删除，则在DB Browser for SQLite软件中，选中对应码表，右击删除表即可，然后在小科狗中呼出候选框界面，再次"保存内存数据库到硬盘"即可。</p>

                        <h4>大词库功能</h4>
                        <p>小科狗支持百万级大词库功能，实现方式为将码表命名为以"大词库"开头即可。例如原有码表名为"86五笔"，要想实现百万级大词库支持功能，将码表命名为"大词库86五笔"即可。</p>
                        <div class="step-note">
                            <strong>注意：</strong>百万级大词库功能会影响到调频等功能，望周知。
                        </div>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h3>反查功能</h3>
                        <p>小科狗可以进行字词反查，例如在使用形码输入法时，不知道某些字的拆分方法，可以利用拼音快速反查进行拆分学习，再例如不知道某些字词的读音，可以利用反查快速获取。</p>

                        <h4>反查的开启与关闭</h4>
                        <p>小科狗助手可以利用快捷键（ctrl+j）开启或关闭反查内容（注意，请在候选框显示的情况下按快捷键，否则无效）。当呼出候选框后，按ctrl+j，如果编码串位置提示"反查显示已关闭"，则候选项不再显示反查内容，反之则显示反查内容。</p>

                        <h4>利用小科狗配置助手进行反查</h4>
                        <p>小科狗配置助手具有划选反查功能。具体设置为在"码表"设置项下勾选"划选反查"，此时，如果输入字词上屏，用鼠标选中该字词，按鼠标中键（滚轮），即弹出反查界面。</p>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h3>引导码表设置</h3>
                        <p>小科狗的特色之一，即为可以利用三个引导键引导多达6个任意码表，其设置在码表配置界面最下面的"引导码表检索设置"项。</p>
                        <p>引导快捷键必须是英文状态小写符号。默认第一个引导键为`键（Tab上方按键），第二个引导键为数字1，第三个引导键为2。如果要对各引导键进行更新，直接替换默认按键即可，并且各引导键所引导的码表必须是当前码表数据库已有码表，否则引导无效。当在输入时按一下引导键引导第一个码表，连按两个引导键则引导第二个码表。</p>

                        <h4>使用示例：</h4>
                        <p>以86五笔为例，在平时输入时主码表为86五笔，而第一个引导键为分号键，引导了两个码表：英文宝典和拼音反查。也就是说在利用86五笔输入时，按一下分号键，进入临时码表：英文宝典，此时输入时，直接输入编码串（key），出现的即为词条列（value）。</p>
                    </div>
                </div>

                <div class="tutorial-step">
                    <div class="step-number">5</div>
                    <div class="step-content">
                        <h3>主辅码表切换</h3>
                        <p>小科狗另外一个特色功能是可以利用快捷键进行主辅码表切换：当给主码表设置了辅码表后，如果敲击快捷键，即可直接切换到辅码表，再次敲击快捷键，回到主码表。此功能在码表配置界面中的"临时码表检索"项设置。</p>

                        <h4>使用示例：</h4>
                        <p>例如当前主码表是86五笔，给其设置的辅码表为英文宝典，引导快捷键为`（Tab上面的按键）。主码表没有设置辅码表时，候选框界面编码串前面没有数字出现，如果设置了辅码表，则编码串前面有1：出现，表示当前为主码表状态。</p>

                        <p>在给主码表设置有辅码表的情况下，按引导快捷键，进行辅码表输入状态，此时输入时，不再是主码表，而是辅码表了，并且编码串前面有2：出现，表示当前为辅码表状态。</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 问题解决 -->
    <section id="troubleshooting" class="tutorial-section">
        <div class="container">
            <h2 class="section-title">问题解决</h2>
            <div class="tutorial-content">
                <div class="intro-box">
                    <h3>常见问题与解决方案</h3>
                    <p>在使用小科狗输入法的过程中，您可能会遇到一些问题。本节收集了常见问题及其解决方案，帮助您快速解决使用中的困难。</p>
                </div>

                <div class="troubleshooting-content">
                    <div class="problem-item">
                        <h3>❌ 安装后无法启动小科狗输入法怎么办？</h3>
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                <li>确认已经给 KegServer.exe 设置了管理员权限</li>
                                <li>确认已经运行了对应的安装程序（32位或64位）</li>
                                <li>检查是否有安全软件阻止了程序运行</li>
                                <li>尝试重启电脑后再次启动</li>
                            </ol>
                        </div>
                    </div>

                    <div class="problem-item">
                        <h3>🐌 候选窗口不显示或显示异常怎么办？</h3>
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                <li>检查候选窗口是否被设置为隐藏（按Ctrl+上下箭头调整大小）</li>
                                <li>重新配置候选窗口设置</li>
                                <li>检查字体设置是否正确</li>
                                <li>尝试重启小科狗服务</li>
                            </ol>
                        </div>
                    </div>

                    <div class="problem-item">
                        <h3>📝 输入法切换不了或无法输入中文怎么办？</h3>
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                <li>确认小科狗服务已经启动（系统托盘有小科狗图标）</li>
                                <li>检查输入法是否已经正确安装到系统中</li>
                                <li>尝试使用不同的切换方式（Ctrl+Shift 或 Win+Space）</li>
                                <li>重新安装输入法</li>
                            </ol>
                        </div>
                    </div>

                    <div class="problem-item">
                        <h3>🔧 码表无法导入或导入后无法使用怎么办？</h3>
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                <li>确认码表格式正确（包含key、value、weight、fc四个字段）</li>
                                <li>检查码表文件编码是否为UTF-8</li>
                                <li>导入后记得"保存内存数据库到硬盘"</li>
                                <li>重启小科狗服务后再次尝试</li>
                            </ol>
                        </div>
                    </div>

                    <div class="problem-item">
                        <h3>💾 配置修改后不生效怎么办？</h3>
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                <li>修改配置后已经保存文件</li>
                                <li>已经重启了小科狗服务</li>
                                <li>配置文件路径正确</li>
                                <li>配置语法格式正确</li>
                            </ol>
                        </div>
                    </div>

                    <div class="problem-item">
                        <h3>🎨 如何卸载小科狗输入法？</h3>
                        <div class="solution">
                            <h4>解决方案：</h4>
                            <ol>
                                <li>运行安装目录下的卸载程序</li>
                                <li>点击"卸载"按钮</li>
                                <li>按提示完成卸载过程</li>
                                <li>如需要，手动删除残留的配置文件</li>
                            </ol>
                        </div>
                    </div>
                </div>

                <div class="help-section">
                    <h3>获取更多帮助</h3>
                    <p>如果以上解决方案无法解决您的问题，您可以：</p>
                    <ul class="step-list">
                        <li>加入小科狗官方QQ群寻求帮助</li>
                        <li>查看更详细的官方文档</li>
                        <li>观看视频教程</li>
                        <li>向有经验的用户请教</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>


    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>小科狗输入法</h4>
                    <p>永远免费的输入法平台</p>
                    <div class="footer-social">
                        <a href="#" title="QQ群">💬</a>
                        <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" title="B站">📺</a>
                        <a href="https://www.toutiao.com/c/user/token/MS4wLjABAAAAsItj-RKKszxsyp_4Y_D82QpBTYN4X13J_rt3giogb1B1ZEiJDQZTJAQLw_ZJv1SP" target="_blank" title="头条">📰</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="index.html#download">下载软件</a></li>
                        <li><a href="tutorial.html">使用手册</a></li>
                        <li><a href="index.html#tutorial">视频教程</a></li>
                        <li><a href="index.html#community">加入社区</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <ul>
                        <li>QQ群1: 641858743</li>
                        <li>QQ群2: 498060191</li>
                        <li>QQ群3: 641389627</li>
                        <li>网站: xiaokegou.com</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>支持我们</h4>
                    <p>如果您觉得小科狗输入法好用，欢迎打赏支持开发者继续改进产品。</p>
                    <button class="btn btn-donate">❤️ 打赏支持</button>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 小科狗输入法. 保留所有权利. | 网站: xiaokegou.com</p>
                <p>Keep Earth Green - 让输入更美好</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
