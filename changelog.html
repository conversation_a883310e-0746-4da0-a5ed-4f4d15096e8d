<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>更新日志 - 小科狗输入法</title>
    <meta name="description" content="小科狗输入法版本更新日志，了解最新功能改进和问题修复。">
    <meta name="keywords" content="小科狗输入法,更新日志,版本历史,KEG输入法,功能更新">
    <link rel="stylesheet" href="css/style.css">
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <img src="images/logo.png" alt="小科狗输入法" class="logo-img">
                <span class="logo-text">小科狗输入法</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">首页</a></li>
                <li><a href="index.html#features">特色功能</a></li>
                <li><a href="index.html#download">下载</a></li>
                <li><a href="tutorial.html">教程</a></li>
                <li><a href="changelog.html" class="active">更新日志</a></li>
                <li><a href="index.html#community">社区</a></li>
                <li><a href="index.html#about">关于</a></li>
            </ul>
            <div class="nav-toggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- 更新日志页面头部 -->
    <section class="changelog-hero">
        <div class="container">
            <h1>小科狗更新日志</h1>
            <p class="changelog-subtitle">记录每一次进步，见证小科狗的成长历程</p>
            <div class="changelog-stats">
                <div class="stat-item">
                    <span class="stat-number">2025</span>
                    <span class="stat-label">持续更新</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100+</span>
                    <span class="stat-label">版本迭代</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">功能改进</span>
                </div>
            </div>
        </div>
    </section>

    <!-- 版本更新列表 -->
    <section class="changelog-content">
        <div class="container">
            <!-- 2025年5月更新 -->
            <div class="version-block">
                <div class="version-header">
                    <div class="version-info">
                        <h2 class="version-number">2025年5月</h2>
                        <span class="version-date">2025年5月16日</span>
                        <span class="version-tag current">最新</span>
                    </div>
                    <div class="version-summary">
                        <p>功能增强，新增状态栏自定义和编码串个性化选项</p>
                    </div>
                </div>

                <div class="changelog-categories">
                    <div class="changelog-category">
                        <h3 class="category-title">🚀 新增功能</h3>
                        <ul class="changelog-list">
                            <li>增加状态栏缩放和隐藏方案名选项，具体见配置界面之"全局设置"相关配置项</li>
                            <li>增加编码串序号自定义、码表标签色、主副码表标识色</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">🔧 功能改进</h3>
                        <ul class="changelog-list">
                            <li>修改小狗截图全局命令默认快键（5月12日）</li>
                            <li>优化小狗中在英雄联盟游戏中英文切换的问题（5月12日）</li>
                            <li>匹配皮肤文件默认项：如果没有发现Keg.gif文件，会寻找Keg.png文件，如果还没有，会报错（5月7日）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 2025年4月更新 -->
            <div class="version-block">
                <div class="version-header">
                    <div class="version-info">
                        <h2 class="version-number">2025年4月</h2>
                        <span class="version-date">2025年4月26日</span>
                    </div>
                    <div class="version-summary">
                        <p>新增皮肤菜单和间隔锁屏功能，界面配置更加灵活</p>
                    </div>
                </div>

                <div class="changelog-categories">
                    <div class="changelog-category">
                        <h3 class="category-title">🚀 新增功能</h3>
                        <ul class="changelog-list">
                            <li>增加皮肤菜单（4月26日）</li>
                            <li>增加间隔锁屏（4月26日）</li>
                            <li>增加"候选到边框线的间距"配置项（4月9日）</li>
                            <li>增加"候选窗口候选排列方向模式>1时隐藏编码串行时无候选时，编码转候选1吗？=不要》"设置项（4月7日）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 2025年3月更新 -->
            <div class="version-block">
                <div class="version-header">
                    <div class="version-info">
                        <h2 class="version-number">2025年3月</h2>
                        <span class="version-date">2025年3月31日</span>
                    </div>
                    <div class="version-summary">
                        <p>通讯接口重大更新，支持标点符号自动配对，兼容性大幅提升</p>
                    </div>
                </div>

                <div class="changelog-categories">
                    <div class="changelog-category">
                        <h3 class="category-title">🚀 新增功能</h3>
                        <ul class="changelog-list">
                            <li>增加支持中英文标点符号自动配对，最大27对（3月15日）</li>
                            <li>支持嵌入字体色自定义（3月12日）</li>
                            <li>增加键盘按键全局钩子跟tsf按键事件相互配合（3月8日）</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">🔧 功能改进</h3>
                        <ul class="changelog-list">
                            <li>默认自动为消息内存通讯，如消息内存不起作用，则自动切换到命名管道通讯（3月31日）</li>
                            <li>优化自动造词功能（3月31日）</li>
                            <li>优化按键监控（3月8日）</li>
                            <li>优化检测键按下的代码（3月3日）</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">🐛 问题修复</h3>
                        <ul class="changelog-list">
                            <li>修复在Adobe家的PDF软件无法打字上屏的问题（3月23日）</li>
                            <li>修复重复上屏跟引导打字字数的统计问题（3月1日）</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">⚠️ 重要提醒</h3>
                        <ul class="changelog-list">
                            <li>更新因改了通讯接口，需要重新安装，更新tsf夹，更新服务端，更新配置端（3月12日）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 2025年2月更新 -->
            <div class="version-block">
                <div class="version-header">
                    <div class="version-info">
                        <h2 class="version-number">2025年2月</h2>
                        <span class="version-date">2025年2月26日</span>
                    </div>
                    <div class="version-summary">
                        <p>标点功能优化，文档更新，界面设置增强</p>
                    </div>
                </div>

                <div class="changelog-categories">
                    <div class="changelog-category">
                        <h3 class="category-title">🚀 新增功能</h3>
                        <ul class="changelog-list">
                            <li>增加大写状态，自动标点下为英文标点，增加自动标点提示（2月26日）</li>
                            <li>新增状态栏右键菜单皮肤设置入口（2月13日）</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">🔧 功能改进</h3>
                        <ul class="changelog-list">
                            <li>因语雀分享链接有分享时间限制，故同时开通知乎在线说明（2月24日）</li>
                            <li>右键菜单添加知乎在线文字说明（2月24日）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 2024年更新 -->
            <div class="version-block">
                <div class="version-header">
                    <div class="version-info">
                        <h2 class="version-number">2024年</h2>
                        <span class="version-date">2024年3月26日</span>
                    </div>
                    <div class="version-summary">
                        <p>配置工具发布，光标跟随优化，整句模式增强</p>
                    </div>
                </div>

                <div class="changelog-categories">
                    <div class="changelog-category">
                        <h3 class="category-title">🚀 新增功能</h3>
                        <ul class="changelog-list">
                            <li>小科狗配置GUI工具发布。请于下载链接下载试用（3月26日）</li>
                            <li>整句模式支持修改词条（3月21日）</li>
                            <li>添加字典查询功能，具体使用方法详见2.24节（2月4日）</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">🔧 功能改进</h3>
                        <ul class="changelog-list">
                            <li>智能光标跟随优化（3月24日）</li>
                            <li>office软件搜索界面优化（3月19日）</li>
                            <li>UAC相关优化（3月14日）</li>
                            <li>修复三种检索命令直通车；优化过渡态上屏（2月28日）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 2023年重要更新 -->
            <div class="version-block">
                <div class="version-header">
                    <div class="version-info">
                        <h2 class="version-number">2023年</h2>
                        <span class="version-date">2023年12月</span>
                    </div>
                    <div class="version-summary">
                        <p>皮肤系统、状态栏功能、标点配对等重要功能发布</p>
                    </div>
                </div>

                <div class="changelog-categories">
                    <div class="changelog-category">
                        <h3 class="category-title">🚀 新增功能</h3>
                        <ul class="changelog-list">
                            <li>添加自定义皮肤功能，支持静态和动态皮肤（12月16日）</li>
                            <li>添加状态栏皮肤文字位置定义功能（12月19日）</li>
                            <li>添加嵌入时下划线颜色自定义功能（12月18日）</li>
                            <li>添加数字大写转化功能（11月6日）</li>
                            <li>添加临时计算功能（7月30日）</li>
                            <li>添加临时英文长句功能（5月28日）</li>
                            <li>添加状态栏（5月3日）</li>
                        </ul>
                    </div>

                    <div class="changelog-category">
                        <h3 class="category-title">🔧 功能改进</h3>
                        <ul class="changelog-list">
                            <li>候选框选中项底色添加圆角效果（12月9日）</li>
                            <li>分离检索，异步检索（12月8日）</li>
                            <li>候选框圆角可以进行独立设置（11月29日）</li>
                            <li>升级命名管道为十线程，设两个空闲等待线程（11月19日）</li>
                            <li>优化候选窗口显示：全面采用透明度显示或隐藏候选窗口（5月22日）</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 更多版本提示 -->
            <div class="more-versions">
                <h3>查看完整更新历史</h3>
                <p>以上仅展示了近期的主要更新，想了解更详细的版本更新历史？</p>
                <div style="margin-top: 20px;">
                    <a href="https://www.yuque.com/gongzixiaobai-ypdqo/tnrbgm/yfqze3" target="_blank" class="btn btn-primary" style="margin-right: 15px;">语雀完整文档</a>
                    <a href="https://zhuanlan.zhihu.com/p/25084621174" target="_blank" class="btn btn-secondary">知乎在线说明</a>
                </div>
                <p style="margin-top: 15px; font-size: 0.9rem; color: #666;">
                    小科狗输入法自2022年以来持续更新，累计发布了数百个版本，修复了大量问题，新增了众多实用功能。
                </p>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>小科狗输入法</h4>
                    <p>永远免费的输入法平台</p>
                    <div class="footer-social">
                        <a href="#" title="QQ群">💬</a>
                        <a href="https://www.bilibili.com/video/BV1rrPAejEEv/" target="_blank" title="B站">📺</a>
                        <a href="https://www.toutiao.com/c/user/token/MS4wLjABAAAAsItj-RKKszxsyp_4Y_D82QpBTYN4X13J_rt3giogb1B1ZEiJDQZTJAQLw_ZJv1SP" target="_blank" title="头条">📰</a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>快速链接</h4>
                    <ul>
                        <li><a href="index.html#download">下载软件</a></li>
                        <li><a href="tutorial.html">使用手册</a></li>
                        <li><a href="changelog.html">更新日志</a></li>
                        <li><a href="index.html#community">加入社区</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>联系我们</h4>
                    <ul>
                        <li>QQ群1: 641858743</li>
                        <li>QQ群2: 498060191</li>
                        <li>QQ群3: 641389627</li>
                        <li>网站: xiaokegou.com</li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>支持我们</h4>
                    <p>如果您觉得小科狗输入法好用，欢迎打赏支持开发者继续改进产品。</p>
                    <button class="btn btn-donate">❤️ 打赏支持</button>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 小科狗输入法. 保留所有权利. | 网站: xiaokegou.com</p>
                <p>Keep Earth Green - 让输入更美好</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
